package models

import (
	corev1 "k8s.io/api/core/v1"

	"transwarp.io/aip/llmops-common/pkg/expense"
)

const (
	TenantUidPrefix = "llmops-"
)

type Tenant struct {
	TenantName           string                        `json:"tenant_name" gorm:"column:name;type:varchar(500);"`
	TenantUid            string                        `json:"tenant_uid" gorm:"primary_key;column:uid;type:varchar(64);"`
	TenantDescription    string                        `json:"tenant_description" gorm:"column:description;type:text;"`
	TenantLogo           string                        `json:"tenant_logo" gorm:"column:logo;type:varchar(1000);"`
	TenantQuotas         TenantResourceQuota           `json:"tenant_quotas" gorm:"-"`
	TenantStatus         string                        `json:"status" gorm:"-"`
	Creator              string                        `json:"creator" gorm:"column:creator;type:varchar(500);"`
	CreateTime           uint64                        `json:"createTime" gorm:"column:create_time"`
	TenantLabels         map[string]string             `json:"-" gorm:"-"`
	TenantAnnotations    map[string]string             `json:"-" gorm:"-"`
	TccUrl               string                        `json:"tcc_url" gorm:"-"`
	ClusterNamespace     string                        `json:"cluster_namespace" gorm:"-"` // TDC5 的 一个租户对应的cluster namespace不一定和 tenant uid 一致
	HippoServiceName     string                        `json:"hippo_service_name"`
	ResourceGroupDetails []expense.ResourceGroupTenant `json:"resource_group_details" gorm:"-"`
	ResourceGroupIDs     []string                      `json:"resource_group_ids" gorm:"-"`
	ProjectIDInfos       []ProjectIDInfo               `json:"project_infos" gorm:"-"`
	CanDelete            bool                          `json:"can_delete" gorm:"-"`
}

func (t *Tenant) DeleteSafe(tdcModel bool) bool {
	if tdcModel {
		return false
	}
	for _, rg := range t.ResourceGroupDetails {
		if !rg.TenantStatus.Idle {
			return false
		}
	}
	return true
}

type ProjectIDInfo struct {
	ProjectID   string `json:"project_id"`
	ProjectName string `json:"project_name"`
	Creator     string `json:"creator"`
}

type TenantResourceQuota struct {
	NameSpace string            `json:"namespace"`
	QuotaName string            `json:"quota_name"`
	Hard      ResourceQuotaSpec `json:"hard"`
	Used      ResourceQuotaSpec `json:"used"`
	Limits    ResourceQuotaSpec `json:"limits"`

	QuotaItemAttributes []Attribute `json:"quota_item_attributes"`

	QuotaType QuotaType `json:"quota_type"`
}

type QuotaType string

const (
	QuotaTypeDynamic QuotaType = "dynamic"
	QuotaTypeFixed   QuotaType = "fixed"
)

type ResourceQuotaSpec struct {
	QuotaType QuotaType `json:"quota_type"`

	// max pods
	Pods            string `json:"pods" yaml:"pods"`
	LimitsCpu       string `json:"limits_cpu" yaml:"limits_cpu"`
	LimitsMemory    string `json:"limits_memory" yaml:"limits_memory"`
	RequestsCpu     string `json:"requests_cpu" yaml:"requests_cpu"`
	RequestsMemory  string `json:"requests_memory" yaml:"requests_memory"`
	RequestsStorage string `json:"requests_storage" yaml:"requests_storage"`

	// network
	Bandwidth        string `json:"bandwidth" yaml:"bandwidth"`
	EgressBandwidth  string `json:"egress_bandwidth" yaml:"egress_bandwidth"`
	IngressBandwidth string `json:"ingress_bandwidth" yaml:"ingress_bandwidth"`

	// deprecated Gpu GpuMemory
	Gpu       string `json:"gpu" yaml:"gpu"`
	GpuMemory string `json:"gpu_memory" yaml:"gpu_memory"`

	// file storage and knowledge  base storage
	// deprecated Knowl
	Knowl                string `json:"knowl" yaml:"knowl"`
	KnowledgeBaseStorage string `json:"knowledge_base_storage" yaml:"knowledege_base_storage"`
	FileStorage          string `json:"file_storage" yaml:"file_storage"`

	// compute resource
	CPU          ResourceQuotaItem               `json:"cpu" yaml:"cpu,omitempty"`
	Memory       ResourceQuotaItem               `json:"memory" yaml:"memory,omitempty"`
	XPU          map[string][]*ResourceQuotaItem `json:"xpu" yaml:"accelerated_computing,omitempty"`
	XPUTypeCount int                             `json:"xpu_type_count" yaml:"xpu_type_count"`

	// queue quota
	Queue *QueueResourceQuota `json:"queue" yaml:"queue,omitempty"`

	WeightMode bool `json:"weight_mode" yaml:"weight_mode"`
}

type QueueResourceQuota struct {
	Default QueueResourceQuotaSpec `json:"default" yaml:"default,omitempty"`
	Task    QueueResourceQuotaSpec `json:"task" yaml:"task,omitempty"`
	Infer   QueueResourceQuotaSpec `json:"infer" yaml:"infer,omitempty"`
}

type QueueResourceQuotaSpec struct {
	CPU    ResourceQuotaItem               `json:"cpu" yaml:"cpu,omitempty"`
	Memory ResourceQuotaItem               `json:"memory" yaml:"memory,omitempty"`
	XPU    map[string][]*ResourceQuotaItem `json:"xpu" yaml:"accelerated_computing,omitempty"`
}

type QuotaItemType string

const (
	QuotaItemTypeCPU      QuotaItemType = "cpu"
	QuotaItemTypeMem      QuotaItemType = "mem"
	QuotaItemTypeXPUCores QuotaItemType = "xpu_cores"
	QuotaItemTypeXPUMem   QuotaItemType = "xpu_mem"
)

type ResourceQuotaItem struct {
	Name           corev1.ResourceName `json:"name" yaml:"name"`
	NominalQuota   string              `json:"nominal_quota" yaml:"nominal_quota"`
	BorrowingLimit string              `json:"borrowing_limit,omitempty" yaml:"borrowing_limit,omitempty"`
	LendingLimit   string              `json:"lending_limit,omitempty" yaml:"lending_limit,omitempty"`

	Unit string `json:"unit" yaml:"unit"`

	QuotaItemType QuotaItemType `json:"quota_item_type" yaml:"quota_item_type"`

	Weight float64 `json:"weight" yaml:"weight"`
}

type Attribute struct {
	Name           string `json:"name"`
	AllowExpansion bool   `json:"allow_expansion"`
	Limit          string `json:"limit"`
}

type Instance struct {
	Id     string `json:"id"`
	Name   string `json:"name"`
	Status string `json:"status"`
}

type TenantEnv struct {
	GuardianAccessToken string `json:"guardian_access_token"`
	Strategy            string `json:"strategy"`
	TDC5Address         string `json:"tdc5_address"`
}

type TenantStatusLocationUrl struct {
	Location string `json:"location"`
}

type TenantIDs struct {
	IDs []string `json:"ids"`
}

func ApplyDefaultQuotaForQueue(spec *ResourceQuotaSpec) {
	if nil == spec.Queue {
		spec.Queue = &QueueResourceQuota{}
	}
	ApplyDefaultQuotaItemType(spec)

	spec.Queue.Default.CPU = spec.CPU
	spec.Queue.Default.Memory = spec.Memory
	spec.Queue.Default.XPU = spec.XPU
	spec.Queue.Task.CPU = spec.CPU
	spec.Queue.Task.Memory = spec.Memory
	spec.Queue.Task.XPU = spec.XPU
	spec.Queue.Infer.CPU = spec.CPU
	spec.Queue.Infer.Memory = spec.Memory
	spec.Queue.Infer.XPU = spec.XPU
}

func ApplyDefaultQuotaItemType(spec *ResourceQuotaSpec) {
	if spec.CPU.QuotaItemType == "" {
		spec.CPU.QuotaItemType = QuotaItemTypeCPU
	}
	if spec.Memory.QuotaItemType == "" {
		spec.Memory.QuotaItemType = QuotaItemTypeMem
	}
	if nil != spec.Queue && spec.Queue.Default.CPU.QuotaItemType == "" {
		spec.Queue.Default.CPU.QuotaItemType = QuotaItemTypeCPU
	}
	if nil != spec.Queue && spec.Queue.Default.Memory.QuotaItemType == "" {
		spec.Queue.Default.Memory.QuotaItemType = QuotaItemTypeMem
	}
	if nil != spec.Queue && spec.Queue.Task.CPU.QuotaItemType == "" {
		spec.Queue.Task.CPU.QuotaItemType = QuotaItemTypeCPU
	}
	if nil != spec.Queue && spec.Queue.Task.Memory.QuotaItemType == "" {
		spec.Queue.Task.Memory.QuotaItemType = QuotaItemTypeMem
	}
	if nil != spec.Queue && spec.Queue.Infer.CPU.QuotaItemType == "" {
		spec.Queue.Infer.CPU.QuotaItemType = QuotaItemTypeCPU
	}
	if nil != spec.Queue && spec.Queue.Infer.Memory.QuotaItemType == "" {
		spec.Queue.Infer.Memory.QuotaItemType = QuotaItemTypeMem
	}
}

func (spec *ResourceQuotaSpec) ApplyNewQuota(newSpec *ResourceQuotaSpec) {
	// copy nominal quota
	spec.CPU.NominalQuota = newSpec.CPU.NominalQuota
	spec.Memory.NominalQuota = newSpec.Memory.NominalQuota
	for xpuType, items := range newSpec.XPU {
		for _, item := range items {
			for _, u := range spec.XPU[xpuType] {
				if u.Name == item.Name {
					u.NominalQuota = item.NominalQuota
					break
				}
			}
		}
	}
	// copy queue nominal quota
	if nil != newSpec.Queue {
		spec.Queue.Default.applyNewQuotaForQueue(&newSpec.Queue.Default)
		spec.Queue.Task.applyNewQuotaForQueue(&newSpec.Queue.Task)
		spec.Queue.Infer.applyNewQuotaForQueue(&newSpec.Queue.Infer)
	}
}

func (spec *QueueResourceQuotaSpec) applyNewQuotaForQueue(newSpec *QueueResourceQuotaSpec) {
	spec.CPU.NominalQuota = newSpec.CPU.NominalQuota
	spec.Memory.NominalQuota = newSpec.Memory.NominalQuota
	for xpuType, items := range newSpec.XPU {
		for _, item := range items {
			for _, u := range spec.XPU[xpuType] {
				if u.Name == item.Name {
					u.NominalQuota = item.NominalQuota
					break
				}
			}
		}
	}
}
