package models

import (
	"encoding/json"
	"fmt"
	"testing"
)

func TestDeleteSafe(t *testing.T) {
	str := `{"tenant_uid":"tenantktestxxxx","tenant_name":"testxxxxxx","resource_group_ids":["cc91d918-62cd-402d-9fc0-9aa23ee07fdd"],"tenant_quotas":{"hard":{"pods":"1k","limits_cpu":"64","limits_memory":"256Gi","requests_cpu":"","requests_memory":"","requests_storage":"","bandwidth":"100Mi","egress_bandwidth":"","ingress_bandwidth":"","gpu":"200","gpu_memory":"80Gi","knowl":"600Gi","knowledge_base_storage":"100Gi","file_storage":"100Gi","cpu":{"name":"cpu","nominal_quota":"2"},"memory":{"name":"memory","nominal_quota":"10Gi"},"accelerated_computing":{"310P3":[{"name":"huawei.com/Ascend310P","nominal_quota":"1"},{"name":"huawei.com/Ascend310P-memory","nominal_quota":"21Gi"}],"910B4":[{"name":"huawei.com/Ascend910B","nominal_quota":"1"},{"name":"huawei.com/Ascend910B-memory","nominal_quota":"32Gi"}],"nvidia":[{"name":"nvidia.com/gpu","nominal_quota":"1"},{"name":"nvidia.com/gpumem","nominal_quota":"20Gi"}]},"queue":{"default":{"cpu":{"name":"cpu","nominal_quota":"0"},"memory":{"name":"memory","nominal_quota":"0Gi"},"accelerated_computing":{"310P3":[{"name":"huawei.com/Ascend310P","nominal_quota":"0"},{"name":"huawei.com/Ascend310P-memory","nominal_quota":"0Gi"}],"910B4":[{"name":"huawei.com/Ascend910B","nominal_quota":"0"},{"name":"huawei.com/Ascend910B-memory","nominal_quota":"0Gi"}],"nvidia":[{"name":"nvidia.com/gpu","nominal_quota":"0"},{"name":"nvidia.com/gpumem","nominal_quota":"0Gi"}]}},"task":{"cpu":{"name":"cpu","nominal_quota":"2"},"memory":{"name":"memory","nominal_quota":"10Gi"},"accelerated_computing":{"310P3":[{"name":"huawei.com/Ascend310P","nominal_quota":"1"},{"name":"huawei.com/Ascend310P-memory","nominal_quota":"21Gi"}],"910B4":[{"name":"huawei.com/Ascend910B","nominal_quota":"1"},{"name":"huawei.com/Ascend910B-memory","nominal_quota":"32Gi"}],"nvidia":[{"name":"nvidia.com/gpu","nominal_quota":"1"},{"name":"nvidia.com/gpumem","nominal_quota":"20Gi"}]}},"infer":{"cpu":{"name":"cpu","nominal_quota":"2"},"memory":{"name":"memory","nominal_quota":"10Gi"},"accelerated_computing":{"310P3":[{"name":"huawei.com/Ascend310P","nominal_quota":"1"},{"name":"huawei.com/Ascend310P-memory","nominal_quota":"21Gi"}],"910B4":[{"name":"huawei.com/Ascend910B","nominal_quota":"1"},{"name":"huawei.com/Ascend910B-memory","nominal_quota":"32Gi"}],"nvidia":[{"name":"nvidia.com/gpu","nominal_quota":"1"},{"name":"nvidia.com/gpumem","nominal_quota":"20Gi"}]}}}}}}`
	var tenant Tenant
	if err := json.Unmarshal([]byte(str), &tenant); err != nil {
		fmt.Println(err.Error())
	} else {
		fmt.Println("xxxx")
	}
}

func TestResourceQuotaSpec(t *testing.T) {
	spec := ResourceQuotaSpec{
		Memory: ResourceQuotaItem{
			NominalQuota: "10",
		},
		Queue: &QueueResourceQuota{
			Default: QueueResourceQuotaSpec{
				Memory: ResourceQuotaItem{
					NominalQuota: "4Gi",
				},
			},
		},
	}
	spec.FixMemAndXpuMemQuotaUnitIfAbsence()
	fmt.Println(spec)
}
