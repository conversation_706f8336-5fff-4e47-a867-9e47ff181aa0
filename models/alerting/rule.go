package alerting

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/go-openapi/strfmt"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/central-auth-service/clients"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/models/monitor"
	gfmodels "transwarp.io/applied-ai/grafana-openapi-client-go/models"
)

const (
	MaxDataPoints = 43200
	EXPR          = "__expr__"
	IntervalMs    = 1000
)

var (
	ConditionRef = "D" // final condition ref id
	NoDataState  = "OK"
	ExecErrState = "Error"

	DefaultRelativeTimeRangeTo   = "0s"
	DefaultRelativeTimeRangeFrom = "10m"
)

type AlertLevel int32

const (
	AlertLevelLow AlertLevel = iota
	AlertLevelMedium
	AlertLevelHigh
)

type ReducerType string

const (
	ReducerTypeLast   ReducerType = "last"
	ReducerTypeMin    ReducerType = "min"
	ReducerTypeMax    ReducerType = "max"
	ReducerTypeMean   ReducerType = "mean"
	ReducerTypeSum    ReducerType = "sum"
	ReducerTypeMedian ReducerType = "median"
)

type EvaluatorType string

const (
	EvaluatorTypeGreater  EvaluatorType = "gt"
	EvaluatorTypeLesser   EvaluatorType = "lt"
	EvaluatorOutsideRange EvaluatorType = "outside_range"
	EvaluatorWithinRange  EvaluatorType = "within_range"
)

type Evaluator struct {
	Type   EvaluatorType `json:"type" description:"比较方式, gt|lt|outside_range|within_range"`
	Params []float64     `json:"params"`
}

type OperatorType string

const (
	OperatorTypeAnd OperatorType = "and"
	OperatorTypeOr  OperatorType = "or"
)

type RelativeTimeRange struct {
	From string `json:"from" description:"时间区间的开始, 相对于now之前多久。Duration格式, 如1m30s表示一分半"`
	To   string `json:"to" description:"时间区间的结束, 相对于now之前多少多久。Duration格式, 如1m30s表示一分半"`
}

func (r RelativeTimeRange) toGrafana() (*gfmodels.RelativeTimeRange, error) {
	if r.From == "" {
		r.From = DefaultRelativeTimeRangeFrom
	}
	if r.To == "" {
		r.To = DefaultRelativeTimeRangeTo
	}
	fromDur, err := strfmt.ParseDuration(r.From)
	if err != nil {
		return nil, err
	}

	toDur, err := strfmt.ParseDuration(r.To)
	if err != nil {
		return nil, err
	}

	return &gfmodels.RelativeTimeRange{
		From: gfmodels.Duration(fromDur.Seconds()),
		To:   gfmodels.Duration(toDur.Seconds()),
	}, nil
}

type ConditionQueryParams struct {
	Step *int `json:"step,omitempty" description:"聚合时间粒度(时间段长度), 毫秒"`
}

type Condition struct {
	Operator    OperatorType          `json:"operator" description:"与前一个条件的关系(第一条为and), and|or"`
	Metric      monitor.Metric        `json:"metric" description:"条件使用的查询指标"`
	QueryParams *ConditionQueryParams `json:"query_params,omitempty" description:"查询参数"`
	Reducer     ReducerType           `json:"reducer" description:"聚合函数, last|min|max|mean|sum|median"`
	TimeRange   RelativeTimeRange     `json:"time_range" description:"时间区间"`
	Evaluator   Evaluator             `json:"evaluator" description:"比较方式"`
}

func (c *Condition) queryModel(refId string, qc helper.QueryContext) (map[string]any, error) {
	if c.QueryParams != nil {
		if c.QueryParams.Step != nil {
			step := *c.QueryParams.Step
			qc[helper.VarStep] = fmt.Sprintf("%dms", step)
		}
	}
	expr, err := c.Metric.RenderExpr(qc)
	if err != nil {
		return nil, stderr.Wrap(err, "render metric [%v] expression with params [%v]", c.Metric, c.QueryParams)
	}
	// editorMode: code
	// expr: llmops_svc_visit_count_total{serviceId="f78c8457-4816-4579-89d7-63e3ababc20f"}
	// instant: false
	// intervalMs: 1000
	// legendFormat: __auto
	// maxDataPoints: 43200
	// range: true
	// refId: A
	intervalMs := IntervalMs
	if c.QueryParams != nil && c.QueryParams.Step != nil {
		intervalMs = *c.QueryParams.Step
	}
	model := map[string]any{
		"editorMode":    "code",
		"expr":          expr,
		"instant":       false,
		"intervalMs":    intervalMs,
		"legendFormat":  "__auto",
		"maxDataPoints": MaxDataPoints,
		"range":         true,
		"refId":         refId,
	}
	return model, nil
}

func (c *Condition) reduceModel(fromRefId, refId string) map[string]any {
	intervalMs := IntervalMs
	if c.QueryParams != nil && c.QueryParams.Step != nil {
		intervalMs = *c.QueryParams.Step
	}
	// - refId: B
	// datasourceUid: __expr__
	// model:
	//   conditions:
	// 	  - evaluator:
	// 		  params: []
	// 		  type: gt
	// 		operator:
	// 		  type: and
	// 		query:
	// 		  params:
	// 			  - B
	// 		reducer:
	// 		  params: []
	// 		  type: last
	// 		type: query
	//   datasource:
	// 	  type: __expr__
	// 	  uid: __expr__
	//   expression: A
	//   intervalMs: 1000
	//   maxDataPoints: 43200
	//   reducer: mean
	//   refId: B
	//   type: reduce
	return map[string]any{
		"conditions": []map[string]any{
			{
				"evaluator": map[string]any{
					"params": []any{},
					"type":   "gt",
				},
				"operator": map[string]any{
					"type": "and",
				},
				"query": map[string]any{
					"params": []any{
						refId,
					},
				},
				"reducer": map[string]any{
					"params": []any{},
					"type":   "last",
				},
			},
		},
		"datasource": map[string]any{
			"type": EXPR,
			"uid":  EXPR,
		},
		"expression":    fromRefId,
		"intervalMs":    intervalMs,
		"maxDataPoints": MaxDataPoints,
		"reducer":       c.Reducer,
		"refId":         refId,
		"type":          "reduce",
	}
}

func (c *Condition) thresholdModel(fromRefId, refId string) map[string]any {
	intervalMs := IntervalMs
	if c.QueryParams != nil && c.QueryParams.Step != nil {
		intervalMs = *c.QueryParams.Step
	}
	// - refId: C
	// datasourceUid: __expr__
	// model:
	//   conditions:
	// 	  - evaluator:
	// 		  params:
	// 			  - 30
	// 		  type: gt
	// 		operator:
	// 		  type: and
	// 		query:
	// 		  params:
	// 			  - C
	// 		reducer:
	// 		  params: []
	// 		  type: last
	// 		type: query
	//   datasource:
	// 	  type: __expr__
	// 	  uid: __expr__
	//   expression: B
	//   intervalMs: 1000
	//   maxDataPoints: 43200
	//   refId: C
	//   type: threshold
	return map[string]any{
		"datasource": map[string]any{
			"type": EXPR,
			"uid":  EXPR,
		},
		"expression":    fromRefId,
		"intervalMs":    intervalMs,
		"maxDataPoints": MaxDataPoints,
		"refId":         refId,
		"type":          "threshold",
		"conditions": []map[string]any{
			{
				"evaluator": map[string]any{
					"params": c.Evaluator.Params,
					"type":   c.Evaluator.Type,
				},
				"operator": map[string]any{
					"type": "and",
				},
				"query": map[string]any{
					"params": []string{refId},
				},
				"reducer": map[string]any{
					"params": []string{},
					"type":   "last",
				},
				"type": "query",
			},
		},
	}
}

type AlertRule struct {
	provisioned         *gfmodels.ProvisionedAlertRule `json:"-" gorm:"-"`
	Id                  int64                          `json:"id" gorm:"column:id;primaryKey;autoIncrement:true"`
	Name                string                         `json:"name" gorm:"column:name;type:varchar(255)" description:"预警名称"`
	ServiceId           string                         `json:"service_id" gorm:"column:serviceId;type:varchar(255)" description:"关联的服务id"`
	ServiceName         string                         `json:"service_name" gorm:"column:serviceName;type:varchar(255)" description:"关联的服务名称"`
	ServiceCreator      string                         `json:"service_creator" gorm:"column:serviceCreator;type:varchar(255)" description:"关联的服务创建者"`
	ServiceVersions     []string                       `json:"service_versions" gorm:"column:serviceVersions;serializer:json" description:"关联的服务版本, 为空表示所有版本"`
	ProjectId           string                         `json:"project_id" gorm:"column:projectId;type:varchar(255)"`
	Level               AlertLevel                     `json:"level" gorm:"column:level;type:int" description:"预警等级, 0:一般, 1:中等, 2:严重"`
	Interval            string                         `json:"interval" gorm:"column:interval;type:varchar(255)" description:"检测间隔, 需为10秒的整数倍。Duration格式, 如1m30s表示一分半"`
	For                 string                         `json:"for" gorm:"column:for;type:varchar(255)" description:"持续时间(持续多久才触发报警), 需要大于等于检测间隔。Duration格式, 如1m30s表示一分半"`
	IsPaused            bool                           `json:"is_paused" gorm:"column:isPaused" description:"是否暂停检测"`
	Description         string                         `json:"description" gorm:"column:description;type:varchar(500)" description:"预警信息"`
	NotificationMessage string                         `json:"notification_message" gorm:"column:notificationMessage;type:text" description:"通知消息内容"`
	RelatedMetrics      []monitor.MetricRef            `json:"related_metrics" gorm:"relatedMetrics;serializer:json" description:"监控到的指标列表"`
	Conditions          []*Condition                   `json:"conditions" gorm:"conditions;serializer:json" description:"触发条件"`
	CreateTime          time.Time                      `gorm:"column:create_time_mills;type:timestamp;default:CURRENT_TIMESTAMP()" json:"create_time" description:"创建时间"`
	UpdateTime          time.Time                      `gorm:"column:update_time_mills;type:timestamp;default:CURRENT_TIMESTAMP()" json:"update_time" description:"更新时间"`
	Creator             string                         `gorm:"column:creator;type:varchar(200)" json:"creator" description:"创建用户"`
}

func (r *AlertRule) UpdateFrom(target *AlertRule) *AlertRule {
	r.Name = target.Name
	r.Level = target.Level
	r.Interval = target.Interval
	r.For = target.For
	r.IsPaused = target.IsPaused
	r.Description = target.Description
	r.NotificationMessage = target.NotificationMessage
	r.RelatedMetrics = target.RelatedMetrics
	r.Conditions = target.Conditions
	return r
}

func (r *AlertRule) OnUpdate() {
	r.UpdateTime = time.Now()
	r.updateRelatedMetrics()
}

func (r *AlertRule) updateRelatedMetrics() {
	metrics := make(map[monitor.MetricRef]struct{})
	for _, c := range r.Conditions {
		if c.Metric.Id == "" {
			continue
		}
		metrics[monitor.MetricRef{Id: c.Metric.Id, Name: c.Metric.Name}] = struct{}{}
	}
	for m := range metrics {
		r.RelatedMetrics = append(r.RelatedMetrics, m)
	}
}

func (r *AlertRule) description() map[string]string {
	return map[string]string{"rule_id": fmt.Sprint(r.Id)}
}

func (r *AlertRule) descriptionJson() string {
	m := r.description()
	b, _ := json.Marshal(m)
	return string(b)
}

func (r *AlertRule) uid() string {
	return RuleUID(r.Id)
}

func (r *AlertRule) queryContext() helper.QueryContext {
	qc := helper.QueryContext{
		helper.VarServiceId: r.ServiceId,
	}

	if len(r.ServiceVersions) == 0 {
		qc[helper.VarVersionIds] = ".*"
	} else {
		qc[helper.VarVersionIds] = strings.Join(r.ServiceVersions, "|")
	}

	return qc
}

// alertQueries returns the alert queries for grafana
// A: query metric data from prometheus
// B: reduce the query result
// C: compare the result with threshold
// D: final math expression to combine all conditions
func (r *AlertRule) alertQueries() (ret []*gfmodels.AlertQuery, err error) {
	if len(r.Conditions) == 0 {
		return
	}
	var finalExpr string
	for i, c := range r.Conditions {
		queryRefId := fmt.Sprintf("A%d", i)
		reduceRefId := fmt.Sprintf("B%d", i)
		thresholdRefId := fmt.Sprintf("C%d", i)

		rtr, err := c.TimeRange.toGrafana()
		if err != nil {
			return nil, err
		}

		queryModel, err := c.queryModel(queryRefId, r.queryContext())
		if err != nil {
			return nil, err
		}
		aq := &gfmodels.AlertQuery{
			RefID:             queryRefId,
			DatasourceUID:     conf.C.Grafana.DatasourceUid,
			RelativeTimeRange: rtr,
			Model:             queryModel,
		}
		bq := &gfmodels.AlertQuery{
			RefID:         reduceRefId,
			DatasourceUID: EXPR,
			Model:         c.reduceModel(queryRefId, reduceRefId),
		}
		cq := &gfmodels.AlertQuery{
			RefID:         thresholdRefId,
			DatasourceUID: EXPR,
			Model:         c.thresholdModel(reduceRefId, thresholdRefId),
		}
		ret = append(ret, aq, bq, cq)
		exprVar := fmt.Sprintf("$%s", thresholdRefId)
		if i > 0 {
			if c.Operator == OperatorTypeAnd {
				finalExpr = fmt.Sprintf("%s && %s", finalExpr, exprVar)
			} else {
				finalExpr = fmt.Sprintf("%s || %s", finalExpr, exprVar)
			}
		} else {
			finalExpr = exprVar
		}
	}
	dq := &gfmodels.AlertQuery{
		RefID:         ConditionRef,
		DatasourceUID: EXPR,
		Model:         mathExprModel(finalExpr),
	}
	ret = append(ret, dq)

	return ret, nil
}

func mathExprModel(expr string) map[string]any {
	// 	conditions:
	// 	- evaluator:
	// 		params:
	// 			- 0
	// 			- 0
	// 		type: gt
	// 	  operator:
	// 		type: and
	// 	  query:
	// 		params: []
	// 	  reducer:
	// 		params: []
	// 		type: avg
	// 	  type: query
	// datasource:
	// 	name: Expression
	// 	type: __expr__
	// 	uid: __expr__
	// expression: $C
	// hide: false
	// intervalMs: 1000
	// maxDataPoints: 43200
	// refId: D
	// type: math
	return map[string]any{
		"conditions": []map[string]any{
			{
				"evaluator": map[string]any{
					"params": []float64{0, 0},
					"type":   "gt",
				},
				"operator": map[string]any{
					"type": "and",
				},
				"query": map[string]any{
					"params": []string{},
				},
				"reducer": map[string]any{
					"params": []string{},
					"type":   "avg",
				},
				"type": "query",
			},
		},
		"datasource": map[string]any{
			"name": "Expression",
			"type": EXPR,
			"uid":  EXPR,
		},
		"expression":    expr,
		"hide":          false,
		"intervalMs":    IntervalMs,
		"maxDataPoints": MaxDataPoints,
		"refId":         ConditionRef,
		"type":          "math",
	}
}

func (r *AlertRule) Provisioned() (*gfmodels.ProvisionedAlertRule, error) {
	if r.provisioned == nil {
		uid := r.uid()

		forDur := new(strfmt.Duration)
		err := forDur.UnmarshalText([]byte(r.For))
		if err != nil {
			return nil, err
		}
		queries, err := r.alertQueries()
		if err != nil {
			return nil, stderr.Wrap(err, "make alert queries")

		}

		ruleGroup, err := r.ruleGroupName()
		if err != nil {
			return nil, stderr.Wrap(err, "get rule group name")
		}
		r.provisioned = &gfmodels.ProvisionedAlertRule{
			UID:          uid,
			Title:        &uid,
			Condition:    &ConditionRef,
			Data:         queries,
			NoDataState:  &NoDataState,
			ExecErrState: &ExecErrState,
			For:          forDur,
			Annotations: map[string]string{
				"description": r.descriptionJson(),
			},
			IsPaused: r.IsPaused,
			// NotificationSettings: &gfmodels.AlertRuleNotificationSettings{
			// 	Receiver: &(conf.C.Alerting.ReceiverName),
			// },
			NotificationSettings: nil,
			Labels: map[string]string{
				"rule_origin": "llmops",
			},
			FolderUID: &clients.GrafanaFolder,
			OrgID:     &clients.GrafanaOrgID,
			RuleGroup: &ruleGroup,
		}

		if err = r.provisioned.Validate(nil); err != nil {
			return nil, stderr.Wrap(err, "validate provisioned alert rule")
		}
	}

	return r.provisioned, nil
}

func (r *AlertRule) ruleGroupName() (string, error) {
	d := new(strfmt.Duration)
	err := d.UnmarshalText([]byte(r.Interval))
	if err != nil {
		return "", err
	}
	return "interval-" + d.String(), nil
}

func (r *AlertRule) IntervalSeconds() (int64, error) {
	d, err := time.ParseDuration(r.Interval)
	if err != nil {
		return 0, err

	}
	return int64(d.Seconds()), nil
}

func RuleUID(id int64) string {
	return fmt.Sprintf("llmops-alert-rule-%d", id)
}
