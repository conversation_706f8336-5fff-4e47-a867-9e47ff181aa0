package models

type ProjectSummary struct {
	ProjectTotalCount int `json:"project_total_count"`
	UserTotalCount    int `json:"user_total_count"`
	ServiceTotalCount int `json:"service_total_count"`
}

type AssetsOverview struct {
	ModelOverview         *ModelOverview         `json:"model_overview"`
	ApplicationOverview   *ApplicationOverview   `json:"application_overview"`
	KnowledgeBaseOverview *KnowledgeBaseOverview `json:"knowledge_base_overview"`
	CorupsOverview        *CorupsOverview        `json:"corups_overview"`
}

type ModelOverview struct {
	ModelTotalCount        int `json:"model_total_count"`
	ModelVersionTotalCount int `json:"model_version_total_count"`
}
type ApplicationOverview struct {
	ApplicationTotalCount          int `json:"applet_total_count"`
	PublishedApplicationTotalCount int `json:"published_application_total_count"`
}
type KnowledgeBaseOverview struct {
	KnowledgeBaseTotalCount      int `json:"knowledge_base_total_count"`
	KnowledgeBaseChunkTotalCount int `json:"knowledge_base_chunk_total_count"`
}
type CorupsOverview struct {
	DatasetTotalCount    int `json:"dataset_total_count"`
	FileAssetsTotalCount int `json:"file_assets_total_count"`
}

type StaticAssetsQueryReq struct {
	Page           int             `json:"page"`
	PageSize       int             `json:"page_size"`
	AssetsSubtypes []AssetsSubtype `json:"assets_subtypes"`
	Creator        string          `json:"creator"`
	SortBy         string          `json:"sort_by"`
	SortOrder      string          `json:"sort_order"`
}

type StaticAssetsResp struct {
	Total int                   `json:"total"`
	Data  []*StaticAssetsDetail `json:"details"`
}

// call applet model corups api
type StaticAssetsDetail struct {
	Name          string        `json:"name"`
	ProjectName   string        `json:"project_name"`
	ProjectID     string        `json:"project_id"`
	Status        string        `json:"status"`
	AssetsType    AssetsType    `json:"assets_type"`
	AssetsSubtype AssetsSubtype `json:"assets_subtype"`
	UpdateTime    int64         `json:"update_time"`
	Creator       string        `json:"creator"`
	Description   string        `json:"description"`
	RefID         string        `json:"ref_id"`
}

type AssetsType string

const (
	Model       AssetsType = "Model"
	Application AssetsType = "Application"
	Corpus      AssetsType = "Corpus"
	Knowledge   AssetsType = "Knowledge"
)

type AssetsSubtype string

const (
	MwhTypeModel         AssetsSubtype = "Mwh-Model"
	MwhTypeRemoteService AssetsSubtype = "Mwh-RemoteService"

	CorpusTypeDataset   AssetsSubtype = "Corpus-Dataset"
	CorpusTypeFileAsset AssetsSubtype = "Corpus-FileAsset"

	KbsTypeText  AssetsSubtype = "Kbs-Text"
	KbsTypeTable AssetsSubtype = "Kbs-Table"

	AppletTypeChain            AssetsSubtype = "Applet-Chain"
	AppletTypeAssistant        AssetsSubtype = "Applet-Assistant"
	AppletTypeExternalRegister AssetsSubtype = "Applet-External"
	AppletTypeTaskChain        AssetsSubtype = "Applet-TaskChain"
	AppletTypeCustomDeployed   AssetsSubtype = "Applet-Custom"
)

// not define using exists
// call service api
type DynamicAssetsDetails struct {
	Name    string `json:"name"`
	Project string `json:"project"`
	Status  string `json:"status"`
	Type    string `json:"type"`

	UpdateTime  int64  `json:"update_time"`
	LimitTime   int64  `json:"limit_time"`
	Description string `json:"description"`
}
