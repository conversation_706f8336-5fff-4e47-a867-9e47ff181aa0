server:
  name: "cas"
  addr: ":80" # 用户服务与项目管理接口地址
  #  cas_addr: ":8393" # 单点登录服务地址
  cas_addr: ":27281" # 单点登录服务地址
  ssl_cert_path: "./etc/secure/cert.pem"
  ssl_key_path: "./etc/secure/key.pem"
  lite_mode: false

client:
  prefix: "http://autocv-gateway-service:80"
  mwh_suffix: "mw/api/v1/mwh/models/-/search-data"
  sample_suffix: "cv/api/samplemgr/datasets/-/search-data"
  mlops_suffix: "applet/api/v1/applet/models/-/search-data"
  knowledge_base_suffix: "applet/api/v1/knowlhub/kbs/-/search-data"

login_page:
  title_zh: "Sophon LLMOps"
  title_en: "Sophon LLMOps"
#  enable_oauth2: true
#  oauth2_auth_url:  "https://sso.transwarp.io/auth/realms/Test01/protocol/openid-connect/auth"


auth:
  allow_multiclient_login_at_same_time: true
  cookie: "SOPHONID"
  password_free_set_cookie: true
  token: "eyJhbGciOiJIUzUxMiJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJyb2xlcyI6IltcInB1YmxpY1wiLFwiYWRtaW5cIixcIlNPUEhPTl9CQVNJQ1wiLFwiU09QSE9OX0FETUlOXCIsXCJwdWJsaWNcIixcIlNPUEhPTl9CQVNJQ1wiXSIsInNjb3BlIjoiaW50ZXJuYWwiLCJleHAiOjQ3NzU1OTYzMDAsImlhdCI6MTYyMTk5NjMwMH0.y05l_mPJIWScT2TbWVtOLOXykekTuADoBkCkPzzPhnErmAijqW8ReOV4F-FbJTGVP9HXZGZAUfBH8dbVb6bviw"
  ip_whitelist_strict: false # 白名单是否执行严格模式, false: x-real-ip=="" 时直接放行, true: x-real-ip=="" 时拦截
  mode: "mixed" # [ cas | oauth2 | mixed ]
  cas:
    # 使用外部CAS时，跳转的CAS服务地址
    # server_url: "https://************:8393/cas"
    server_url: ""
    ticket_validity:
      login_ticket: 120s
      service_ticket: 30s
      ticket_granting_ticket: 168h
    # 使用内置CAS服务时，外部可访问的跳转链接
    embedded_server_host: "localhost:27280"
    embedded_server_external_host: "" # **************:27280

  # 如果启用了OAuth2登录方式, 请配置以下参数
  # 当前默认配置为星环内部测试SSO, 如有需要可联系IT: 孙邦涵 申请正式生产SSO Client对接
  # US_AUTH.OAUTH2.CLIENT_ID=llmops
  # US_AUTH.OAUTH2.CLIENT_SECRET=2f2cc37d-6ad8-492b-84a3-7a7c79c35162
  # US_AUTH.OAUTH2.ENDPOINT.AUTH_URL="https://sso.transwarp.io/auth/realms/Test01/protocol/openid-connect/auth"
  # US_AUTH.OAUTH2.ENDPOINT.TOKEN_URL="https://sso.transwarp.io/auth/realms/Test01/protocol/openid-connect/token"
  # US_AUTH.OAUTH2.ENDPOINT.AUTH_STYLE=0
  # US_AUTH.OAUTH2.REDIRECT_URL=http://**************:27281/api/v1/oauth2/auth
  oauth2:
    pre_check_redirect: false
    # 用于对接OAth2的自定义参数
    default_user_conf:
      password: Warp1234
      project: assets
      user_group: "all_users" # 默认包含 all_users, 如自定义用户组,可使用逗号分割
    # token_mode 标识采用什么Token处理方式 : [custom - 自定义字段映射 | transwarp - 星环标准SSO  ]
    token_mode: "transwarp"
    # fields_map 用于将解析或者响应的用户结构体转换为指定的结构体(Go Template语法)
    # 仅在 token_mode 为 custom 时生效
    fields_map:
      username: "{{.userCode}}"
      email: "{{.Email}}"
      fullname: "{{.userName}}"
#      roles: "{{.Roles}}"

    # ClientID is the application's ID.
    client_id: llmops

    # ClientSecret is the application's secret.
    client_secret: 2f2cc37d-6ad8-492b-84a3-7a7c79c35162

    # Endpoint contains the resource server's token endpoint
    # URLs. These are constants specific to each server and are
    # often available via site-specific packages, such as
    # google.Endpoint or github.Endpoint.
    endpoint:
      auth_url: "https://sso.transwarp.io/auth/realms/Test01/protocol/openid-connect/auth"

      logout_url: "https://sso.transwarp.io/auth/realms/Test01/protocol/openid-connect/logout"
      device_auth_url: ""
      token_url: "https://sso.transwarp.io/auth/realms/Test01/protocol/openid-connect/token"

      # AuthStyle optionally specifies how the endpoint wants the
      # client ID & client secret sent. The zero value means to
      # auto-detect.
      auth_style: 0

    # RedirectURL is the URL to redirect users going through
    # the OAuth flow, after the resource owner's URLs.
    redirect_url: https://**************:8393/api/v1/oauth2/auth

    # Scope specifies optional requested permissions.
    scopes: [ ]

    user_info_fetcher:
      endpoint: "https://oidc.cregc.com.cn/nepoch-oidc/userinfo"
      method: "GET"
      headers: {}
      query_params:
        access_token: "{{.AccessToken}}"
      body: ""
      response_map:
        username: "{{ .account }}"
        fullname: "{{ .name }}"
        email: "{{ .email }}"
        mobile: "{{ .mobilephone }}"
#
#  # 中铁二局
#  oauth2:
#    pre_check_redirect: true
#    default_user_conf:
#      password: Warp1234
#      project: assets
#      user_groups: [""]
#    token_mode: "external"
#    # fields_map 用于将解析或者响应的用户结构体转换为指定的结构体(Go Template语法)
#    # 仅在 token_mode 为 custom 时生效
#    fields_map:
#      username: "{{.userCode}}"
#      email: "{{.Email}}"
#      fullname: "{{.userName}}"
#    client_id: ztejLLMops
#    client_secret: c3e565d1-4b97-553c-27cc-7fcf4b534f01
#    endpoint:
#      auth_url: https://oidc.cregc.com.cn/nepoch-oidc/authorize
#      logout_url: ""
#      device_auth_url: ""
#      token_url: https://oidc.cregc.com.cn/nepoch-oidc/token
#      auth_style: 0
#    redirect_url: https://ml.cregc.com.cn:31000/llm/llmops/gateway/cas/api/v1/oauth2/auth
#    scopes: ["openid", "email", "profile", "address", "phone"]
#    user_info_fetcher:
#      endpoint: "https://oidc.cregc.com.cn/nepoch-oidc/userinfo"
#      method: "GET"
#      headers: {}
#      query_params:
#        access_token: "{{.AccessToken}}"
#      body: ""
#      response_map:
#        username: "{{ .account }}"
#        fullname: "{{ .name }}"
#        email: "{{ .email }}"
#        mobile: "{{ .mobilephone }}"


init_data:
  create_namespace: false # 是否创建命名空间
  tenants: # 要代替默认初始化的租户数据, 为空创建默认数据
    - project_id: # 空间id
      tenant_uid: # 租户id, 需符合命名空间的命名规范

#db:
#  type: sqlite
#  sqlite:
#    file: data/sqlite.db
#    busy_timeout_ms: 30000
#  debug: true

db:
  type: mysql
  mysql:
    username: "root"
    password: "Warp!CV@2022#"
    host: "autocv-mysql-service"
    port: 3306
    db_name: "metastore_cas_rbac"
    max_idle: 10
    max_conn: 50
    not_print_sql: false
    not_create_table: false
  debug: true

#db:
#  type: mysql
#  mysql:
#    username: "sophonuser"
#    password: "password"
#    host: "************"
#    port: 15307
#    db_name: "metastore_cas_rbac"
#    max_idle: 10
#    max_conn: 50
#    not_print_sql: false
#    not_create_table: false
#  debug: true

session_store:
  type: redis
  memory:
    expire_time: 72h
  # 由于最终前段是通过 SOPHONID 这一Cookie来进行登录鉴权的
  # autocv-gateway 中的 nginx lua 脚本负责将 SOPHONID 映射为对应的 JWT字符串给到后端进行鉴权
  # 当CAS服务同EGW中的Redis配置不一致时，将导致无限循环的跳转，具体表现为：
  # 0. 用户浏览器打开 https://localhost:30443/
  # 1. 前端调用接口鉴权 /gateway/cas/api/users/role -> EGW 根据 SOPHONID 在 RedisB中查找JWT -> 返回 401 （JWT为空）
  # 2. 前端进行登录跳转 /gateway/cas/api/users/login?service=X -> 302重定向至 ${EXTERNAL_CAS}
  # 3. CAS通过TGC完成认证,带ticket返回 -> 302 回 service ${X}?ticket=T
  # 4. 后端校验ticket通过后，跳转回初始地址（步骤0），并将SOPHONID写入 RedisA
  # 5. 1 -> 2 -> ... -> 0 循环产生

redis:
  addrs: "autocv-redis-service:6379"
  database: 0
  username: ""
  password: "Warp!CV@2022#"
  expire_time: 72h
  masterName: ""

logger:
  level: info
  console: true
  age:
    max: 180
  size:
    max: 600

user_store:
  type: local

influxdb:
  #  url: "http://127.0.0.1:30886"
  #  url: "http://**************:32767"
  url: "http://autocv-tsdb-service:8086"
  database: "thinger"
  precision: "ms"
  retention_policy: ""
  write_consistency: ""
  timeout: "30s"
  batch_size: 1000

audit_record:
  render_value_prefix: <span style="color:#a84614">
  render_value_suffix: </span>

health_info:
  model_health_info_url: "http://autocv-mwh-service/api/v1/mwh/svcmgr/services"
  app_health_info_url: "http://autocv-applet-service/api/v1/app/applications/-/services"
#  model_health_info_url: "http://**************:30347/api/v1/mwh/svcmgr/services"
#  app_health_info_url: "http://**************:31219/api/v1/app/applications/-/services"

alerting:
  default_rule_is_paused: false
  receiver_name: llmops-cas-alert


prometheus:
  address: http://prometheus-operator-prometheus.monitor:9090

grafana:
  username: admin
  password: transwarp123
  host: llmops-grafana:80
  debug: false
  datasource_uid: monitor-prometheus
  public_endpoint: /grafana/public-dashboards
