server:
  name: "sophon-user-service"
  addr: ":27281"
  cas_addr: ":27280"
  ssl_cert_path: "./etc/secure/cert.pem"
  ssl_key_path: "./etc/secure/key.pem"


client:
  prefix: "http://autocv-gateway-service:80"
  mwh_suffix: "mw/api/v1/mwh/models/-/search-data"
  sample_suffix: "cv/api/samplemgr/datasets/-/search-data"
  mlops_suffix: "applet/api/v1/applet/models/-/search-data"
  knowledge_base_suffix: "applet/api/v1/knowlhub/kbs/-/search-data"

login_page:
  logo_svg: |
    <svg id="图层_1" data-name="图层 1" xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" fill="currentColor" viewBox="0 0 1066 253.29" > <defs> <style> .cls-1 { fill: #fff; } </style> </defs> <title>资源 25</title><g id="图层_2" data-name="图层 2"><g id="图层_1-2" data-name="图层 1"> <polygon class="cls-1" points="23.89 43.87 120.9 45.78 112.1 64.73 79.36 64.73 54.98 126.77 24.22 126.77 46.34 65.54 14.1 64.73 23.89 43.87" />    <path class="cls-1" d="M134.8,46.51l83.86,2.56s19,26.36-31.13,41.74l-.36,2.2s6.08-3.51,11,11c4.48,13.23,5.48,18.31,5.48,18.31H168.48l-9.14-26,6.59-16.84s20.51.73,22.69-10.25-7.79-7.69-10.11-8.07-16.24.38-16.24.38l-24.91,62.61H103.31Z" /> <polygon class="cls-1" points="211.7 122.67 244.29 122.67 286.77 66.64 285.3 120.47 318.63 120.47 313.14 51.26 272.85 51.26 211.7 122.67" /> <polygon class="cls-1" points="359.99 52.72 397.72 51.26 414.56 91.55 429.21 53.09 459.23 53.09 433.24 119 394.8 119 378.68 83.48 364.03 119 332.17 120.47 359.99 52.72" /> <path class="cls-1" d="M455.22,97H485.6s-1.47,13.18,16.85,11.54,15.74-10.07,15-11.54-8.06-4.75-27.83-7S469.86,75.8,472.06,72.5s10.25-21.24,64.07-19.78c0,0,26.73-1.82,24.54,18.32l-31.86,2.2s2.93-8.8-5.86-8.8-19.77,1.11-19.77,5.49,10.25,5.5,23.81,7.7,26,5.85,25.26,14.65-12.46,26.35-59.33,28.19S454.47,98.87,455.22,97Z" /> <polygon class="cls-1" points="578.25 52.64 607.9 52.64 602.77 91.18 669.79 0 664.66 91.18 690.28 52.64 719.95 52.64 675.28 120.47 638.3 120.47 639.71 75.43 562.5 180.16 578.25 52.64" /> <polygon class="cls-1" points="699.45 120.47 731.67 120.57 773.4 65.91 773.4 120.47 805.99 120.47 800.14 51.26 759.86 51.26 699.45 120.47" /> <path class="cls-1" d="M848.48,48.7l79.82-1.84s7.31,1.84,8.42,12.83-12.83,27.82-34.8,32.21c0,0,11,.75,15.75,32.59H881.79s-5.13-22.33-8-27.81l6.21-18s20.51.37,23.08-10.62-28.56-6.59-28.56-6.59l-23.08,61.15H819.17Z" /> <path class="cls-1" d="M0,166.67s542.68-87,1066,0C1066,166.67,431.49,103.82,0,166.67Z" /> <path class="cls-1" d="M1027.89,44.3c-2.94,0-65.55,2.56-65.55,2.56l-30.76,77.63,31.5,1.48,11.72-28.2a177.06,177.06,0,0,0,28.49-.66c21.76-2.63,37.42-17.29,40.72-32.67S1030.81,44.3,1027.89,44.3ZM980.77,80.17,988,61.69s22.5-6.3,23.4,7.67S985.27,80.17,980.77,80.17Z" /> <path class="cls-1" d="M359,246.44l6.46-7.87c3.39,2.88,7.81,4.87,11.58,4.87,4.23,0,6.21-1.6,6.21-4.16,0-2.76-2.62-3.65-6.72-5.38l-6.08-2.56c-5-2-9.66-6.14-9.66-13.05,0-8,7.16-14.4,17.28-14.4a22.15,22.15,0,0,1,15.48,6.27l-5.69,7.17c-3.14-2.37-6.08-3.65-9.79-3.65-3.52,0-5.7,1.41-5.7,4s3,3.64,7.23,5.31l6,2.36c5.89,2.37,9.41,6.34,9.41,13.06,0,7.94-6.66,14.91-18.17,14.91A26.6,26.6,0,0,1,359,246.44Z" /> <path class="cls-1" d="M412.23,228.33c0-15.61,8.83-24.44,21.82-24.44s21.83,8.89,21.83,24.44-8.83,25-21.83,25S412.23,244,412.23,228.33Zm32,0c0-9.21-3.9-14.65-10.18-14.65s-10.11,5.44-10.11,14.65,3.84,15.11,10.11,15.11S444.23,237.61,444.23,228.33Z" /> <path class="cls-1" d="M476.23,204.78h16.7c10.5,0,19.14,3.71,19.14,15.49,0,11.33-8.77,16.32-18.82,16.32h-5.56v15.8H476.23Zm16.45,22.78c5.57,0,8.25-2.62,8.25-7.29s-3.07-6.46-8.57-6.46h-4.67v13.75Z" /> <path class="cls-1" d="M531.72,204.78h11.45v18.11h15.68V204.78h11.33v47.61H558.85V232.81H543.17v19.58H531.72Z" /> <path class="cls-1" d="M590.6,228.33c0-15.61,8.83-24.44,21.82-24.44s21.82,8.89,21.82,24.44-8.83,25-21.82,25S590.6,244,590.6,228.33Zm32,0c0-9.21-3.91-14.65-10.18-14.65s-10.11,5.44-10.11,14.65,3.84,15.11,10.11,15.11S622.6,237.61,622.6,228.33Z" /> <path class="cls-1" d="M654.6,204.78h11.65l11.83,22.78,4.68,10.56h.32a149.91,149.91,0,0,1-1.54-17.6V204.78h10.88v47.61H680.77l-11.77-23L664.33,219H664c.51,5.31,1.47,11.84,1.47,17.6v15.8H654.6Z" /> </g> </g> </svg>
  title_zh: "Sophon 人工智能平台"
  title_en: "Sophon Artificial Intelligence Platform"

auth:
  cookie: "SOPHONID"
  mode: "cas"
  cas:
    # 使用外部CAS时，跳转的CAS服务地址
    # server_url: "https://************:8393/cas"
    server_url: ""
    ticket_validity:
      login_ticket: 120s
      service_ticket: 30s
      ticket_granting_ticket: 168h
    # 使用内置CAS服务时，外部可访问的跳转链接
    embedded_server_host: "localhost:27280"
    embedded_server_external_host: "" # **************:27280
  password_free_set_cookie: true
  token: "eyJhbGciOiJIUzUxMiJ9.eyJ1c2VybmFtZSI6InRlc3QiLCJyb2xlcyI6IltcInB1YmxpY1wiLFwiYWRtaW5cIixcIlNPUEhPTl9CQVNJQ1wiLFwiU09QSE9OX0FETUlOXCIsXCJwdWJsaWNcIixcIlNPUEhPTl9CQVNJQ1wiXSIsInNjb3BlIjoiaW50ZXJuYWwiLCJleHAiOjQ3NzU1OTYzMDAsImlhdCI6MTYyMTk5NjMwMH0.y05l_mPJIWScT2TbWVtOLOXykekTuADoBkCkPzzPhnErmAijqW8ReOV4F-FbJTGVP9HXZGZAUfBH8dbVb6bviw"


#db:
#  type: sqlite
#  sqlite:
#    file: data/sqlite.db
#    busy_timeout_ms: 30000
#  debug: true

db:
  type: mysql
  mysql:
    username: "root"
    password: "Warp!CV@2022#"
    #    host: "autocv-mysql-service"
    #    port: 3306
    host: "**************"
    port: 31190
    db_name: "metastore_cas_rbac"
    max_idle: 10
    max_conn: 50
    not_print_sql: false
    not_create_table: false
  debug: true

#db:
#  type: mysql
#  mysql:
#    username: "sophonuser"
#    password: "password"
#    host: "************"
#    port: 15307
#    db_name: "metastore_cas_rbac"
#    max_idle: 10
#    max_conn: 50
#    not_print_sql: false
#    not_create_table: false
#  debug: true

session_store:
  type: redis
  memory:
    expire_time: 72h
  # 由于最终前段是通过 SOPHONID 这一Cookie来进行登录鉴权的
  # autocv-gateway 中的 nginx lua 脚本负责将 SOPHONID 映射为对应的 JWT字符串给到后端进行鉴权
  # 当CAS服务同EGW中的Redis配置不一致时，将导致无限循环的跳转，具体表现为：
  # 0. 用户浏览器打开 https://localhost:30443/
  # 1. 前端调用接口鉴权 /gateway/cas/api/users/role -> EGW 根据 SOPHONID 在 RedisB中查找JWT -> 返回 401 （JWT为空）
  # 2. 前端进行登录跳转 /gateway/cas/api/users/login?service=X -> 302重定向至 ${EXTERNAL_CAS}
  # 3. CAS通过TGC完成认证,带ticket返回 -> 302 回 service ${X}?ticket=T
  # 4. 后端校验ticket通过后，跳转回初始地址（步骤0），并将SOPHONID写入 RedisA
  # 5. 1 -> 2 -> ... -> 0 循环产生
redis:
  addrs: "**************:30082"
  database: 0
  password: "Warp!CV@2022#"
  expire_time: 72h
  masterName: ""

logger:
  level: info
  console: true
  age:
    max: 180
  size:
    max: 600

user_store:
  type: local

influxdb:
  #  url: "http://127.0.0.1:30886"
  url: "http://**************:30407"
  #  url: "http://autocv-tsdb-service:8086"
  database: "thinger"
  precision: "ms"
  retention_policy: ""
  write_consistency: ""
  timeout: "30s"
  batch_size: 1000

audit_record:
  render_value_prefix: <span style="color:#a84614">
  render_value_suffix: </span>

health_info:
  model_health_info_url: "http://autocv-mwh-service/api/v1/mwh/svcmgr/services"
  app_health_info_url: "http://autocv-applet-service/api/v1/app/applications/-/services"
#  model_health_info_url: "http://**************:30347/api/v1/mwh/svcmgr/services"
#  app_health_info_url: "http://**************:31219/api/v1/app/applications/-/services"

alerting:
  default_rule_is_paused: false
  receiver_name: llmops-cas-alert

grafana:
  username: admin
  password: transwarp123
  host: llmops-grafana:80
  debug: false
  datasource_uid: monitor-prometheus
  public_endpoint: /grafana/public-dashboards