apiVersion: v1
kind: Config
clusters:
  - cluster:
      certificate-authority: /home/<USER>/workspace/sophon/go/transwarp.io/aip/central-auth-service/etc/kubernetes/ca.pem
      server: https://*************:6443
    name: kubernetes
contexts:
  - context:
      cluster: kubernetes
      user: k8s
    name: kubelet-to-kubernetes
current-context: kubelet-to-kubernetes
users:
  - name: k8s
    user:
      client-certificate: /home/<USER>/workspace/sophon/go/transwarp.io/aip/central-auth-service/etc/kubernetes/admin.pem
      client-key: /home/<USER>/workspace/sophon/go/transwarp.io/aip/central-auth-service/etc/kubernetes/admin-key.pem