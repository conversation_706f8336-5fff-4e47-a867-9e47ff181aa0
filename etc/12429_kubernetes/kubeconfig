apiVersion: v1
kind: Config
clusters:
  - cluster:
      certificate-authority: /srv/kubernetes/ca.pem
      server: https://127.0.0.1:6443
    name: kubernetes
contexts:
  - context:
      cluster: kubernetes
      user: k8s
    name: kubelet-to-kubernetes
current-context: kubelet-to-kubernetes
users:
  - name: k8s
    user:
      client-certificate: /srv/kubernetes/admin.pem
      client-key: /srv/kubernetes/admin-key.pem