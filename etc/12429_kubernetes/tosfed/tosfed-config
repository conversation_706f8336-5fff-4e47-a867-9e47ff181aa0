apiVersion: v1
kind: Config
clusters:
  - cluster:
      certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURtekNDQW9PZ0F3SUJBZ0lVVUtWNzNGcEppclc2V2pHSXE3R1FtZ1pIaVhVd0RRWUpLb1pJaHZjTkFRRUwKQlFBd1hERUxNQWtHQTFVRUJoTUNlSGd4Q2pBSUJnTlZCQWdNQVhneENqQUlCZ05WQkFjTUFYZ3hDakFJQmdOVgpCQW9NQVhneENqQUlCZ05WQkFzTUFYZ3hDekFKQmdOVkJBTU1BbU5oTVJBd0RnWUpLb1pJaHZjTkFRa0JGZ0Y0Ck1DQVhEVEkwTURReU9UQTVNVFF4TUZvWUR6SXhNalF3TkRBMU1Ea3hOREV3V2pCY01Rc3dDUVlEVlFRR0V3SjQKZURFS01BZ0dBMVVFQ0F3QmVERUtNQWdHQTFVRUJ3d0JlREVLTUFnR0ExVUVDZ3dCZURFS01BZ0dBMVVFQ3d3QgplREVMTUFrR0ExVUVBd3dDWTJFeEVEQU9CZ2txaGtpRzl3MEJDUUVXQVhnd2dnRWlNQTBHQ1NxR1NJYjNEUUVCCkFRVUFBNElCRHdBd2dnRUtBb0lCQVFDdGpzUHJNT2NlVW9sbmMyYkswMWRLTkJoN1B6Q0tpdmNtczhRRkxjQkUKZkhTVGhjSE4yeW9wWU8vNnBDMXFSdDcvNGdRNTRvMXJQRTQySWt1dVh3QzAwTXN4UWY3YXhiUTFIZXp6KzFOUgozVTU1N2Y4QWd3NndkM1UwbGptMmFNNVAxVmRKS3BrMUdzdTVGVUlDVDduNTZRdUY0ZlgrK25nTTkrRFBsd3FFClcyWUFKV3I1QVNUWHdlMEVBbE5DRE5rcm9sZmlhU0ZFd29DaUE3R2dOUXd2dTlFSjdqV3BraXV4Nlk5all6WnUKTVpiZG1ZOTVuWU5hblRreHl4NFhwZUQzYkVsOWRRWnFNRTczMEo1TmdzUkdVWWt4eUxjeEhXOFlXL1R3L1Q1bwpZL3grbHhGYkt3eXc3ZWcvY0tqU2VUWi9nRjErbUdlTTVTM1VsNTk0OElLcEFnTUJBQUdqVXpCUk1CMEdBMVVkCkRnUVdCQlJpdHZtZHZqalZRTXlDd2xTZjlYQWxYdmFuTURBZkJnTlZIU01FR0RBV2dCUml0dm1kdmpqVlFNeUMKd2xTZjlYQWxYdmFuTURBUEJnTlZIUk1CQWY4RUJUQURBUUgvTUEwR0NTcUdTSWIzRFFFQkN3VUFBNElCQVFDQgpQWmp5ZmRzMjdpdnJ5eU55QkVHMHc3bjBVcFVJK3pkbTJ4dzh6dWVETjVtdEtqZGxOekMvQlowaHcxQ3JQQU1pCm95eWE4K2U4QTdnQ1AxNUhmTVNoSktINTF6ZnR2VCtET2NtWUk4SzZneDZPbWpjV21pV3VaVmxDS1JjSEJNSk4Kd0xIUlUxaEptK3FUSlNpZE1Rc3duZkM2UENucU9UazI2eGI5T2M2M0t5eG0zRUpMS2NpRk1TUVJzQWhVVXp2cgp6R1JiWG5ieVF6MEVtdnZrN2htelVBMFdUTEd3b0JsYTgrdW1LVDlsNHRHRlUzOXZSRnRlL05SL1Ezencvdzh6ClZCR0gwa3BTNGJIeXozMk1IOWNxNTVnYmprMytEcXBTZE92alFKWUxjUUlCTkxNek91YU9wNzJzcHRIbjVjSm0KQS9BYkh2VEFBd1grbVE5ODMrNlkKLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=
      insecure-skip-tls-verify: false
      server: https://tosfed-apiserver.tosfed-system.svc.transwarp.local:5443
    name: tosfed-apiserver
users:
  - user:
      client-certificate-data: 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
      client-key-data: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    name: tosfed-apiserver
contexts:
  - context:
      cluster: tosfed-apiserver
      user: tosfed-apiserver
    name: tosfed-apiserver
current-context: tosfed-apiserver