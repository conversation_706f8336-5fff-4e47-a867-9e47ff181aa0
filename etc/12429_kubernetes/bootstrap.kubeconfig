apiVersion: v1
clusters:
- cluster:
    certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURVRENDQWppZ0F3SUJBZ0lKQUxHQ25zME90bVdCTUEwR0NTcUdTSWIzRFFFQkN3VUFNQjh4SFRBYkJnTlYKQkFNVUZIUnlZVzV6ZDJGeWNFQXhORFE1TnpJME1EVTFNQjRYRFRFMU1USXhNREExTURjek5Wb1hEVEkxTVRJdwpOekExTURjek5Wb3dIekVkTUJzR0ExVUVBeFFVZEhKaGJuTjNZWEp3UURFME5EazNNalF3TlRVd2dnRWlNQTBHCkNTcUdTSWIzRFFFQkFRVUFBNElCRHdBd2dnRUtBb0lCQVFDaFlGejh2bmxmZzFFSGN5eEhSMERINiswYlRNakgKajJjTEJTODhYdExQOUtqZzUrbnlNSzEybi9JWm9tSjlxbXl6NjBtTHVwQ2w4SGRDdk5KbS9DM0ZRTW1xa016VQpHVjJLMDJUMTA2aVBBcTdna1Blc0toT3U1ODlHMnEybXBzUU5iOFNDMjJyOTl2Q3lOTDRuMUdneGtrdDhveFkyCjQyeTBXN1IydGpWQlhSU3M0R2kycWNhekVWYWRqdE5udGUyZ1Z5L004NW1PVnBlSFMvNVlwNU55SklXN2ZUeEIKMFpBbnduNXFMa2ZhRi9KVWlkZzVoUXNPOFlLSk5oV0FVZFJ2MDJrVVI1YmQvTlVrckROWEFpK0YzaWZNeU43TwpYWnRRZUtqdHVGTUQvU0RSdUxLTXJoSnBydTRGdmlJVDY4Nkk3SW1UcUZ6TklRUGFrdmhBUlAyTEFnTUJBQUdqCmdZNHdnWXN3SFFZRFZSME9CQllFRlBrc2VTaEhEeDJFVEJFN0ZUT0RBd0VRcVlXNE1FOEdBMVVkSXdSSU1FYUEKRlBrc2VTaEhEeDJFVEJFN0ZUT0RBd0VRcVlXNG9TT2tJVEFmTVIwd0d3WURWUVFERkJSMGNtRnVjM2RoY25CQQpNVFEwT1RjeU5EQTFOWUlKQUxHQ25zME90bVdCTUF3R0ExVWRFd1FGTUFNQkFmOHdDd1lEVlIwUEJBUURBZ0VHCk1BMEdDU3FHU0liM0RRRUJDd1VBQTRJQkFRQ2VLYzBaSzlzNjA1eVJyTGFsWDkyaE5vZFFkcTdUYWtNWmZrZFMKL1pDQTdZb0JYZjNIRC9aUm5PZUI0LzNqWEUzN2xDM0RMYWZkRVhWUG93K0VPK254WUJsM0xLcjJ5M0ZkN1NuTQoyTTJQOURBRWZFb0RKekdPMEhiQ0F1eGY4NFh4VUtkTHVETk9OY1l0Q2VuMTZDVzhJWUYxU3lKbzZlQUtFZklRCktqbkNkczcxQXRJeFNJUXlKd0R5Yk1NQ3dSYWNCZlVIVnpIc1pDWk5NRmhXNndEK1lMUWtPMnRSZ1dzM2NNanIKTzFaWm1mSkZtekgvU1JnSXZwcWtjQzllT0w3R2RFRnh0Z0FqM3V3Ti9rb1lFc01XVE5HVGsvc2Q0cm1Gcld3TApZanhOQ3NVSnJhZ3h6WWd5UDBwTnExdUIvYWNnUHhmaWdJWTZVUEpicXA4SDkyRnYKLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=
    server: https://127.0.0.1:6443
  name: kubernetes
contexts:
- context:
    cluster: kubernetes
    user: kubelet-bootstrap
  name: kubernetes
current-context: kubernetes
kind: Config
preferences: {}
users:
- name: kubelet-bootstrap
  user:
    as-user-extra: {}
    token: 02b50b05283e98dd0fd71db496ef01e8