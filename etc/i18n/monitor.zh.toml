[M_SVC_TOTAL_VISIT_COUNT]
name = "总访问量"
description = "服务的累计总访问次数"
unit = "次"
legendFormatPrefix = "总访问量："

[M_SVC_VISIT_COUNT_INCREMENT]
name = "访问次数增量"
description = "服务的访问次数增量"
unit = "次"
legendFormatPrefix = "访问次数增量："

[M_SVC_AVG_DURATION]
name = "平均响应时间"
description = "服务的平均响应时间"
unit = "毫秒(ms)"
legendFormatPrefix = "平均响应时间："

[M_SVC_AVG_TIME_TO_FIRST_TOKEN]
name = "平均首字延时"
description = "服务的平均首字延时"
unit = "毫秒(ms)"
legendFormatPrefix = "平均首字延时："

[M_SVC_AVG_INPUT_TOKENS]
name = "平均输入token数"
description = "服务的平均输入token数量"
unit = "个"
legendFormatPrefix = "平均输入token数："

[M_SVC_AVG_OUTPUT_TOKENS]
name = "平均输出token数"
description = "服务的平均输出token数量"
unit = "个"
legendFormatPrefix = "平均输出token数："

[M_SVC_TOTAL_INPUT_TOKENS]
name = "总输入token数"
description = "服务的累计总输入token数量"
unit = "个"
legendFormatPrefix = "总输入token数："

[M_SVC_TOTAL_OUTPUT_TOKENS]
name = "总输出token数"
description = "服务的累计总输出token数量"
unit = "个"
legendFormatPrefix = "总输出token数："

[M_SVC_AVG_CPU_USAGE_PERC]
name = "CPU平均使用率"
description = "服务的CPU使用率(在聚合时间段内平均)"
unit = "%"
legendFormatPrefix = "CPU平均使用率："

[M_SVC_MEMORY_USAGE]
name = "内存使用量(MB)"
description = "服务的内存使用量(MB)"
unit = "MB"
legendFormatPrefix = "内存使用量(MB)："

[M_SVC_VGPU_CORE_USED]
name = "算力使用率"
description = "服务的算力使用率(按Pod统计)"
unit = "%"
legendFormatPrefix = "算力使用率："

[M_SVC_VGPU_MEMORY_USED]
name = "显存使用量(MB)"
description = "服务的显存使用量(MB)(按Pod统计)"
unit = "MB"
legendFormatPrefix = "显存使用量(MB)："

[M_SVC_VGPU_MEMORY_ALLOCATED]
name = "已分配显存量(MB)"
description = "服务已分配的显存量(MB)"
unit = "MB"
legendFormatPrefix = "已分配显存量(MB)："

[M_SVC_VGPU_CORE_ALLOCATED]
name = "已分配算力"
description = "服务已分配的算力"
unit = "%"
legendFormatPrefix = "已分配算力："

[M_SVC_TOTAL_KNOW_DOC_COUNT]
name = "召回文档总量"
description = "服务的召回文档总量"
unit = "个"
legendFormatPrefix = "召回文档总量:"

[M_SVC_HEALTH_DEGREE]
name = "健康度"
description = "总分100,由50%可用性、30%服务性能、20%资源使用情况构成. 可用性：聚合时间段内的服务请求成功率; 服务性能：暂未评估，保持满分; 资源：显存使用率(占分配量)低于85%时满分，85-100按比例扣分直至0"
unit = "%"
legendFormatPrefix = "健康度："

[M_GPU_CARD_TOTAL]
name = "gpu算力总卡数"
description = "gpu资源概述-算力卡数总量"
unit = "张"
legendFormatPrefix = "gpu算力总卡数："

[M_GPU_CARD_USED]
name = "gpu算力已使用卡数"
description = "gpu资源概述-算力卡数已使用数量"
unit = "张"
legendFormatPrefix = "gpu算力已使用卡数："

[M_GPU_FULLY_LOADED]
name = "gpu算力满载卡数"
description = "gpu资源概述-算力满载卡数"
unit = "张"
legendFormatPrefix = "gpu算力满载卡数："

[M_GPU_PARTIALLY_LOAD]
name = "gpu算力部分负载卡数"
description = "gpu资源概述-部分负载"
unit = "张"
legendFormatPrefix = "gpu算力部分负载卡数："

[M_GPU_RESOURCE_BOTTLENECK]
name = "gpu算力资源瓶颈卡数"
description = "gpu资源概述-资源瓶颈卡数"
unit = "张"
legendFormatPrefix = "gpu算力资源瓶颈卡数："

[M_GPU_RESOURCE_IDLE]
name = "gpu算力资源空闲卡数"
description = "gpu资源概述-空闲卡数"
unit = "张"
legendFormatPrefix = "gpu算力资源空闲卡数："

[M_GPU_VCORE_USAGE]
name = "gpu算力已使用资源"
description = "gpu资源概述-已使用资源"
unit = "个"
legendFormatPrefix = "gpu算力已使用资源："

[M_GPU_VCORE_ALLOCATED]
name = "gpu算力已分配资源"
description = "gpu资源概述-已分配资源"
unit = "个"
legendFormatPrefix = "gpu算力已分配资源："

[M_GPU_VCORE_TOTAL]
name = "gpu算力资源总量"
description = "gpu资源概述-总量"
unit = "个"
legendFormatPrefix = "gpu算力资源总量："

[M_GPU_MEMORY_USAGE]
name = "gpu显存已使用资源"
description = "gpu显存资源概述-已使用资源"
unit = "MB"
legendFormatPrefix = "gpu显存已使用资源："

[M_GPU_MEMORY_ALLOCATED]
name = "gpu显存已分配资源"
description = "gpu显存资源概述-已分配资源"
unit = "MB"
legendFormatPrefix = "gpu显存已分配资源："

[M_GPU_MEMORY_TOTAL]
name = "gpu显存资源总量"
description = "gpu显存资源概述-总量"
unit = "MB"
legendFormatPrefix = "gpu显存资源总量："

[M_GPU_TENANT_DISTRIBUTION]
name = "gpu算力按照租户分布"
description = "gpu算力按照租户分布"
unit = "个"
legendFormatPrefix = "gpu算力按照租户分布："

[M_GPU_NODE_DISTRIBUTION]
name = "gpu算力按照节点分布"
description = "gpu算力按照节点分布"
unit = "个"
legendFormatPrefix = "gpu算力按照节点分布："

[M_GPU_CARD_DISTRIBUTION]
name = "gpu算力按照卡类型分布"
description = "gpu算力按照卡类型分布"
unit = "个"
legendFormatPrefix = "gpu算力按照卡类型分布："

[M_GPU_TREND_VCORE]
name = "算力使用率"
description = "算力使用率"
unit = "%"
legendFormatPrefix = "算力使用率："

[M_GPU_TREND_MEMORY]
name = "显存使用量"
description = "显存使用量"
unit = "MiB"
legendFormatPrefix = "显存使用量："

[M_BASE_CPU_TOTAL]
name = "基础资源cpu总核数"
description = "基础资源cpu总核数"
unit = "个"
legendFormatPrefix = "基础资源cpu总核数："

[M_BASE_CPU_USAGE]
name = "基础资源cpu使用量"
description = "基础资源cpu使用量"
unit = "个"
legendFormatPrefix = "基础资源cpu使用量："

[M_BASE_MEMORY_TOTAL]
name = "基础资源内存总数"
description = "基础资源内存总数"
unit = "MiB"
legendFormatPrefix = "基础资源内存总数："

[M_BASE_MEMORY_USAGE]
name = "基础资源内存使用量"
description = "基础资源内存使用量"
unit = "MiB"
legendFormatPrefix = "基础资源内存使用量："

[M_BASE_DISK_TOTAL]
name = "基础资源磁盘总数"
description = "基础资源磁盘总数"
unit = "MiB"
legendFormatPrefix = "基础资源磁盘总数："

[M_BASE_DISK_USAGE]
name = "基础资源磁盘使用量"
description = "基础资源磁盘使用量"
unit = "MiB"
legendFormatPrefix = "基础资源磁盘使用量："

[M_BASE_TREND_DISK]
name = "基础资源磁盘占比"
description = "基础资源磁盘占用比"
unit = "%"
legendFormatPrefix = "基础资源磁盘占比："

[M_BASE_TREND_MEMORY]
name = "基础资源内存占比"
description = "基础资源内存占用比"
unit = "%"
legendFormatPrefix = "基础资源内存占比："

[M_BASE_TREND_CPU]
name = "基础资源CPU占比"
description = "基础资源CPU占用比"
unit = "%"
legendFormatPrefix = "基础资源CPU占比："

[M_BASE_RANKING_VCORE]
name = "算力使用排行"
description = "算力使用排行"
unit = "个"
legendFormatPrefix = "算力使用排行："

[M_BASE_RANKING_CPU]
name = "CPU使用排行"
description = "CPU使用排行"
unit = "个"
legendFormatPrefix = "CPU使用排行："

[M_BASE_RANKING_DISK]
name = "磁盘使用排行"
description = "磁盘使用排行"
unit = "个"
legendFormatPrefix = "磁盘使用排行："

[M_SERVICE_RANKING_VCORE]
name = "服务算力使用排行"
description = "服务算力使用排行"
unit = "个"
legendFormatPrefix = "服务算力使用排行："

[M_RANKING_VMEMORY]
name = "显存使用排行"
description = "显存使用排行"
unit = "GB"
legendFormatPrefix = "显存使用排行："

[M_SERVICE_RANKING_CPU]
name = "服务CPU使用排行"
description = "服务CPU使用排行"
unit = "个"
legendFormatPrefix = "服务CPU使用排行："

[M_SERVICE_RANKING_MEMORY]
name = "服务内存使用排行"
description = "服务内存使用排行"
unit = "GB"
legendFormatPrefix = "服务内存使用排行："

[M_GPU_USED_VCORE]
name = "算力使用率"
description = "算力使用率"
unit = "%"
legendFormatPrefix = "算力使用率："

[M_GPU_USED_VMEMORY]
name = "显存使用量"
description = "显存使用量"
unit = "MiB"
legendFormatPrefix = "显存使用量："

[M_GPU_ALLOCATED_VCORE]
name = "算力分配率"
description = "算力分配率"
unit = "%"
legendFormatPrefix = "算力分配率："

[M_GPU_ALLOCATED_VMEMORY]
name = "显存分配率"
description = "显存分配率"
unit = "%"
legendFormatPrefix = "显存分配率："

[M_SVC_TENANT_DISTRIBUTION]
name = "服务资源池分布"
description = "服务资源池分布"
unit = "个"
legendFormatPrefix = "服务资源池分布："

[M_SVC_COMPUTE_TYPE_DISTRIBUTION]
name = "算力类型分布"
description = "算力类型分布"
unit = "个"
legendFormatPrefix = "算力类型分布："

[M_SVC_SOURCE_TYPE_DISTRIBUTION]
name = "服务类型分布"
description = "服务类型分布"
unit = "个"
legendFormatPrefix = "服务类型分布："

[M_NPU_INFO]
name = "NPU资源信息"
description = "NPU资源信息"
unit = "个"
legendFormatPrefix = "NPU资源信息："

[M_SVC_SOURCE_STATUS_DISTRIBUTION]
name = "服务状态分布"
description = "服务状态分布"
unit = "个"
legendFormatPrefix = "服务状态分布："

[M_SVC_LIST]
name = "服务列表查询"
description = "服务列表查询"
unit = "个"
legendFormatPrefix = "服务列表查询："

[M_SVC_DETAIL]
name = "服务列表详情"
description = "服务列表详情"
unit = "个"
legendFormatPrefix = "服务列表详情："

[P_SVC_VISIT_COUNT]
name = "总访问量"
description = ""
unit = "次"
legendFormatPrefix = "总访问量："

[P_SVC_VISIT_COUNT_INCREMENT]
name = "访问次数增量"
description = ""
unit = "次"
legendFormatPrefix = "访问次数增量："

[P_SVC_AVG_DURATION]
name = "平均响应时间"
description = ""
unit = "毫秒(ms)"
legendFormatPrefix = "平均响应时间："

[P_SVC_AVG_TIME_TO_FIRST_TOKEN]
name = "平均首字延时"
description = ""
unit = "毫秒(ms)"
legendFormatPrefix = "平均首字延时："

[P_SVC_AVG_INPUT_TOKENS]
name = "平均输入token数"
description = ""
unit = "个"
legendFormatPrefix = "平均输入token数："

[P_SVC_AVG_OUTPUT_TOKENS]
name = "平均输出token数"
description = ""
unit = "个"
legendFormatPrefix = "平均输出token数："

[P_SVC_TOTAL_INPUT_TOKENS]
name = "总输入token数"
description = ""
unit = "个"
legendFormatPrefix = "总输入token数："

[P_SVC_TOTAL_OUTPUT_TOKENS]
name = "总输出token数"
description = ""
unit = "个"
legendFormatPrefix = "总输出token数："

[P_SVC_AVG_CPU_USAGE_PERC]
name = "CPU平均使用率"
description = ""
unit = "%"
legendFormatPrefix = "CPU平均使用率："

[P_SVC_MEMORY_USAGE]
name = "内存使用量(MB)"
description = ""
unit = "MB"
legendFormatPrefix = "内存使用量(MB)："

[P_SVC_VGPU_MEMORY_USED_AND_ALLOCATED]
name = "显存使用/分配(MB)"
description = ""
unit = "MB"
legendFormatPrefix = "显存使用/分配(MB)："

[P_SVC_VGPU_CORE_USED_AND_ALLOCATED]
name = "算力使用/分配"
description = ""
unit = "百分比(%)"
legendFormatPrefix = "算力使用/分配："

[P_SVC_HEALTH_DEGREE]
name = "健康度"
description = ""
unit = "百分比(%)"
legendFormatPrefix = "健康度："

[P_GPU_OVERVIEW]
name = "GPU资源概述"
description = ""
unit = "张"
legendFormatPrefix = "GPU资源概述："

[P_GPU_TREND]
name = "GPU资源使用趋势"
description = ""
unit = "MiB / 百分比(%)"
legendFormatPrefix = "GPU资源使用趋势："

[P_BASE_OVERVIEW]
name = "基础资源概览"
description = ""
unit = "个"
legendFormatPrefix = "基础资源概览："

[P_BASE_TREND]
name = "基础资源趋势"
description = ""
unit = "MiB / 百分比(%)"
legendFormatPrefix = "基础资源趋势："

[P_BASE_RANKING]
name = "基础资源排行"
description = ""
unit = "个"
legendFormatPrefix = "基础资源排行："

[P_SERVICE_RANKING]
name = "服务资源排行"
description = ""
unit = "GB"
legendFormatPrefix = "服务资源排行："

[P_GPU_USAGE]
name = "GPU资源使用情况"
description = ""
unit = "个"
legendFormatPrefix = "GPU资源使用情况："

[P_SVC_OVERVIEW]
name = "服务资源概览"
description = ""
unit = "个"
legendFormatPrefix = "服务资源概览："

[P_SVC_LIST]
name = "服务资源列表查询"
description = ""
unit = "个"
legendFormatPrefix = "服务资源列表查询："

[P_SVC_DETAIL]
name = "服务列表详情查询"
description = ""
unit = "个"
legendFormatPrefix = "服务列表详情查询："

[P_GPU_USED]
name = "GPU资源已使用总量"
description = ""
unit = "个"
legendFormatPrefix = "GPU资源已使用总量："

[P_NPU_INFO]
name = "NPU资源信息"
description = ""
unit = "个"
legendFormatPrefix = "NPU资源信息："

[C_SVC_NotALLOCATED]
name = "未分配"
description = ""

[C_SVC_NOTUSED]
name = "未占用"
description = ""
