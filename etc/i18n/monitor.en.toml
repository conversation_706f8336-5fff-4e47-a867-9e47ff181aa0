[M_SVC_TOTAL_VISIT_COUNT]
name = "Total Visits"
description = "Total cumulative number of visits of the service"
unit = "count"
legendFormatPrefix = "Total Visits:"

[M_SVC_VISIT_COUNT_INCREMENT]
name = "Visit Count Increment"
description = "Increment of service visits"
unit = "count"
legendFormatPrefix = "Visit Count Increment:"

[M_SVC_AVG_DURATION]
name = "Average Response Time"
description = "Average response time of the service"
unit = "ms"
legendFormatPrefix = "Average Response Time:"

[M_SVC_AVG_TIME_TO_FIRST_TOKEN]
name = "Average Time to First Token"
description = "Average first-token latency of the service"
unit = "ms"
legendFormatPrefix = "Average Time to First Token:"

[M_SVC_AVG_INPUT_TOKENS]
name = "Average Number of Input Tokens"
description = "Average number of input tokens of the service"
unit = "count"
legendFormatPrefix = "Average Number of Input Tokens:"

[M_SVC_AVG_OUTPUT_TOKENS]
name = "Average Number of Output Tokens"
description = "Average number of output tokens of the service"
unit = "count"
legendFormatPrefix = "Average Number of Output Tokens:"

[M_SVC_TOTAL_INPUT_TOKENS]
name = "Total Number of Input Tokens"
description = "Total cumulative number of input tokens of the service"
unit = "count"
legendFormatPrefix = "Total Number of Input Tokens:"

[M_SVC_TOTAL_OUTPUT_TOKENS]
name = "Total Number of Output Tokens"
description = "Total cumulative number of output tokens of the service"
unit = "count"
legendFormatPrefix = "Total Number of Output Tokens:"

[M_SVC_AVG_CPU_USAGE_PERC]
name = "Average CPU Usage Rate"
description = "Average CPU usage percentage of the service (over the aggregation interval)"
unit = "%"
legendFormatPrefix = "Average CPU Usage Rate:"

[M_SVC_MEMORY_USAGE]
name = "Memory Usage (MB)"
description = "Memory usage (in MB) of the service"
unit = "MB"
legendFormatPrefix = "Memory Usage (MB):"

[M_SVC_VGPU_CORE_USED]
name = "Compute Usage Rate"
description = "Service compute usage rate (aggregated by pod)"
unit = "%"
legendFormatPrefix = "Compute Usage Rate:"

[M_SVC_VGPU_MEMORY_USED]
name = "GPU Memory Usage (MB)"
description = "GPU memory usage (in MB) of the service (aggregated by pod)"
unit = "MB"
legendFormatPrefix = "GPU Memory Usage (MB):"

[M_SVC_VGPU_MEMORY_ALLOCATED]
name = "Allocated GPU Memory (MB)"
description = "Allocated GPU memory (in MB) of the service"
unit = "MB"
legendFormatPrefix = "Allocated GPU Memory (MB):"

[M_SVC_VGPU_CORE_ALLOCATED]
name = "Allocated Compute"
description = "Compute resources allocated to the service"
unit = "%"
legendFormatPrefix = "Allocated Compute:"

[M_SVC_TOTAL_KNOW_DOC_COUNT]
name = "Total Recalled Documents"
description = "Total number of documents recalled by the service"
unit = "documents"
legendFormatPrefix = "Total Recalled Documents:"

[M_SVC_HEALTH_DEGREE]
name = "Health Score"
description = "Health score of the service (out of 100) based on 50% availability, 30% performance, and 20% resource usage. Availability: success rate of requests during the aggregation period; Performance: not evaluated (full score); Resources: memory usage rate below 85% of allocation yields full score, with proportional deductions from 85-100%."
unit = "%"
legendFormatPrefix = "Health Score:"

[M_GPU_CARD_TOTAL]
name = "Total GPU Compute Cards"
description = "GPU resource overview - total number of compute cards"
unit = "cards"
legendFormatPrefix = "Total GPU Compute Cards:"

[M_GPU_CARD_USED]
name = "Used GPU Compute Cards"
description = "GPU resource overview - number of compute cards in use"
unit = "cards"
legendFormatPrefix = "Used GPU Compute Cards:"

[M_GPU_FULLY_LOADED]
name = "Fully Loaded GPU Cards"
description = "GPU resource overview - number of fully loaded compute cards"
unit = "cards"
legendFormatPrefix = "Fully Loaded GPU Cards:"

[M_GPU_PARTIALLY_LOAD]
name = "Partially Loaded GPU Cards"
description = "GPU resource overview - number of partially loaded compute cards"
unit = "cards"
legendFormatPrefix = "Partially Loaded GPU Cards:"

[M_GPU_RESOURCE_BOTTLENECK]
name = "GPU Resource Bottleneck Cards"
description = "GPU resource overview - number of resource bottleneck cards"
unit = "cards"
legendFormatPrefix = "GPU Resource Bottleneck Cards:"

[M_GPU_RESOURCE_IDLE]
name = "Idle GPU Resource Cards"
description = "GPU resource overview - number of idle cards"
unit = "cards"
legendFormatPrefix = "Idle GPU Resource Cards:"

[M_GPU_VCORE_USAGE]
name = "GPU Compute Resources Used"
description = "GPU resource overview - resources currently in use"
unit = "count"
legendFormatPrefix = "GPU Compute Resources Used:"

[M_GPU_VCORE_ALLOCATED]
name = "GPU Compute Resources Allocated"
description = "GPU resource overview - allocated resources"
unit = "count"
legendFormatPrefix = "GPU Compute Resources Allocated:"

[M_GPU_VCORE_TOTAL]
name = "Total GPU Compute Resources"
description = "GPU resource overview - total resources"
unit = "count"
legendFormatPrefix = "Total GPU Compute Resources:"

[M_GPU_MEMORY_USAGE]
name = "GPU Memory Resources Used"
description = "GPU memory resource overview - used resources"
unit = "MB"
legendFormatPrefix = "GPU Memory Resources Used:"

[M_GPU_MEMORY_ALLOCATED]
name = "GPU Memory Resources Allocated"
description = "GPU memory resource overview - allocated resources"
unit = "MB"
legendFormatPrefix = "GPU Memory Resources Allocated:"

[M_GPU_MEMORY_TOTAL]
name = "Total GPU Memory Resources"
description = "GPU memory resource overview - total resources"
unit = "MB"
legendFormatPrefix = "Total GPU Memory Resources:"

[M_GPU_TENANT_DISTRIBUTION]
name = "GPU Compute Distribution by Tenant"
description = "Distribution of GPU compute across tenants"
unit = "count"
legendFormatPrefix = "GPU Compute Distribution by Tenant:"

[M_GPU_NODE_DISTRIBUTION]
name = "GPU Compute Distribution by Node"
description = "Distribution of GPU compute across nodes"
unit = "count"
legendFormatPrefix = "GPU Compute Distribution by Node:"

[M_GPU_CARD_DISTRIBUTION]
name = "GPU Compute Distribution by Card Type"
description = "Distribution of GPU compute by card type"
unit = "count"
legendFormatPrefix = "GPU Compute Distribution by Card Type:"

[M_GPU_TREND_VCORE]
name = "Compute Usage Rate"
description = "Compute usage rate"
unit = "%"
legendFormatPrefix = "Compute Usage Rate:"

[M_GPU_TREND_MEMORY]
name = "Memory Usage"
description = "Memory usage"
unit = "MiB"
legendFormatPrefix = "Memory Usage:"

[M_BASE_CPU_TOTAL]
name = "Total Base CPU Cores"
description = "Total number of base CPU cores"
unit = "count"
legendFormatPrefix = "Total Base CPU Cores:"

[M_BASE_CPU_USAGE]
name = "Base CPU Usage"
description = "CPU usage in base resources"
unit = "count"
legendFormatPrefix = "Base CPU Usage:"

[M_BASE_MEMORY_TOTAL]
name = "Total Base Memory"
description = "Total base memory"
unit = "MiB"
legendFormatPrefix = "Total Base Memory:"

[M_BASE_MEMORY_USAGE]
name = "Base Memory Usage"
description = "Memory usage in base resources"
unit = "MiB"
legendFormatPrefix = "Base Memory Usage:"

[M_BASE_DISK_TOTAL]
name = "Total Base Disk"
description = "Total base disk"
unit = "MiB"
legendFormatPrefix = "Total Base Disk:"

[M_BASE_DISK_USAGE]
name = "Base Disk Usage"
description = "Disk usage in base resources"
unit = "MiB"
legendFormatPrefix = "Base Disk Usage:"

[M_BASE_TREND_DISK]
name = "Base Disk Usage Ratio"
description = "Ratio of disk usage in base resources"
unit = "%"


[M_BASE_TREND_MEMORY]
name = "Base Memory Usage Ratio"
description = "Ratio of memory usage in base resources"
unit = "%"

[M_BASE_TREND_CPU]
name = "Base CPU Usage Ratio"
description = "Ratio of CPU usage in base resources"
unit = "%"

[M_BASE_RANKING_VCORE]
name = "Compute Usage Ranking"
description = "Ranking of compute usage"
unit = "count"
legendFormatPrefix = "Compute Usage Ranking:"

[M_BASE_RANKING_CPU]
name = "CPU Usage Ranking"
description = "Ranking of CPU usage"
unit = "count"
legendFormatPrefix = "CPU Usage Ranking:"

[M_BASE_RANKING_DISK]
name = "Disk Usage Ranking"
description = "Ranking of disk usage"
unit = "count"
legendFormatPrefix = "Disk Usage Ranking:"

[M_SERVICE_RANKING_VCORE]
name = "Service Compute Usage Ranking"
description = "Ranking of service compute usage"
unit = "count"
legendFormatPrefix = "Service Compute Usage Ranking:"

[M_RANKING_VMEMORY]
name = "GPU Memory Usage Ranking"
description = "Ranking of GPU memory usage"
unit = "GB"
legendFormatPrefix = "GPU Memory Usage Ranking:"

[M_SERVICE_RANKING_CPU]
name = "Service CPU Usage Ranking"
description = "Ranking of service CPU usage"
unit = "count"
legendFormatPrefix = "Service CPU Usage Ranking:"

[M_SERVICE_RANKING_MEMORY]
name = "Service Memory Usage Ranking"
description = "Ranking of service memory usage"
unit = "GB"
legendFormatPrefix = "Service Memory Usage Ranking:"

[M_GPU_USED_VCORE]
name = "Compute Usage Rate"
description = "Compute usage rate"
unit = "%"
legendFormatPrefix = "Compute Usage Rate:"

[M_GPU_USED_VMEMORY]
name = "Memory Usage"
description = "Memory usage"
unit = "MiB"
legendFormatPrefix = "Memory Usage:"

[M_GPU_ALLOCATED_VCORE]
name = "Compute Allocation Rate"
description = "Ratio of compute resources allocated"
unit = "%"
legendFormatPrefix = "Compute Allocation Rate:"

[M_GPU_ALLOCATED_VMEMORY]
name = "Memory Allocation Rate"
description = "Ratio of memory resources allocated"
unit = "%"
legendFormatPrefix = "Memory Allocation Rate:"

[M_SVC_TENANT_DISTRIBUTION]
name = "Service Tenant Distribution"
description = "Distribution of service resource pools"
unit = "count"

[M_SVC_COMPUTE_TYPE_DISTRIBUTION]
name = "Compute Type Distribution"
description = "Distribution of compute types"
unit = "count"

[M_SVC_SOURCE_TYPE_DISTRIBUTION]
name = "Service Type Distribution"
description = "Distribution of service types"
unit = "count"

[M_NPU_INFO]
name = "NPU Resource Information"
description = "NPU resource information"
unit = "count"
legendFormatPrefix = "NPU Resource Information:"

[M_SVC_SOURCE_STATUS_DISTRIBUTION]
name = "Service Status Distribution"
description = "Distribution of service status"
unit = "count"

[M_SVC_LIST]
name = "Service List Query"
description = "Service list query"
unit = "count"

[M_SVC_DETAIL]
name = "Service Details Query"
description = "Service details query"
unit = "count"

[P_SVC_VISIT_COUNT]
name = "Total Visits"
description = ""
unit = "count"
legendFormatPrefix = "Total Visits:"

[P_SVC_VISIT_COUNT_INCREMENT]
name = "Visit Count Increment"
description = ""
unit = "count"
legendFormatPrefix = "Visit Count Increment:"

[P_SVC_AVG_DURATION]
name = "Average Response Time"
description = ""
unit = "ms"
legendFormatPrefix = "Average Response Time:"

[P_SVC_AVG_TIME_TO_FIRST_TOKEN]
name = "Average Time to First Token"
description = ""
unit = "ms"
legendFormatPrefix = "Average Time to First Token:"

[P_SVC_AVG_INPUT_TOKENS]
name = "Average Number of Input Tokens"
description = ""
unit = "count"
legendFormatPrefix = "Average Number of Input Tokens:"

[P_SVC_AVG_OUTPUT_TOKENS]
name = "Average Number of Output Tokens"
description = ""
unit = "count"
legendFormatPrefix = "Average Number of Output Tokens:"

[P_SVC_TOTAL_INPUT_TOKENS]
name = "Total Number of Input Tokens"
description = ""
unit = "count"
legendFormatPrefix = "Total Number of Input Tokens:"

[P_SVC_TOTAL_OUTPUT_TOKENS]
name = "Total Number of Output Tokens"
description = ""
unit = "count"
legendFormatPrefix = "Total Number of Output Tokens:"

[P_SVC_AVG_CPU_USAGE_PERC]
name = "Average CPU Usage (%)"
description = ""
unit = "%"
legendFormatPrefix = "Average CPU Usage (%):"

[P_SVC_MEMORY_USAGE]
name = "Memory Usage (MB)"
description = ""
unit = "MB"
legendFormatPrefix = "Memory Usage (MB):"

[P_SVC_VGPU_MEMORY_USED_AND_ALLOCATED]
name = "GPU Memory Used/Allocated (MB)"
description = ""
unit = "MB"
legendFormatPrefix = "GPU Memory Used/Allocated (MB):"

[P_SVC_VGPU_CORE_USED_AND_ALLOCATED]
name = "Compute Used/Allocated"
description = ""
unit = "%"
legendFormatPrefix = "Compute Used/Allocated:"

[P_SVC_HEALTH_DEGREE]
name = "Health Score"
description = ""
unit = "%"
legendFormatPrefix = "Health Score:"

[P_GPU_OVERVIEW]
name = "GPU Resource Overview"
description = ""
unit = "cards"
legendFormatPrefix = "GPU Resource Overview:"

[P_GPU_TREND]
name = "GPU Resource Usage Trends"
description = ""
unit = "MiB / %"
legendFormatPrefix = "GPU Resource Usage Trends:"

[P_BASE_OVERVIEW]
name = "Base Resource Overview"
description = ""
unit = "count"
legendFormatPrefix = "Base Resource Overview:"

[P_BASE_TREND]
name = "Base Resource Trends"
description = ""
unit = "MiB / %"
legendFormatPrefix = "Base Resource Trends:"

[P_BASE_RANKING]
name = "Base Resource Ranking"
description = ""
unit = "count"
legendFormatPrefix = "Base Resource Ranking:"

[P_SERVICE_RANKING]
name = "Service Resource Ranking"
description = ""
unit = "GB"
legendFormatPrefix = "Service Resource Ranking:"

[P_GPU_USAGE]
name = "GPU Resource Usage"
description = ""
unit = "count"
legendFormatPrefix = "GPU Resource Usage:"

[P_SVC_OVERVIEW]
name = "Service Resource Overview"
description = ""
unit = "count"
legendFormatPrefix = "Service Resource Overview:"

[P_SVC_LIST]
name = "Service Resource List Query"
description = ""
unit = "count"
legendFormatPrefix = "Service Resource List Query:"

[P_SVC_DETAIL]
name = "Service Details Query"
description = ""
unit = "count"
legendFormatPrefix = "Service Details Query:"

[P_GPU_USED]
name = "Total GPU Resources Used"
description = ""
unit = "count"
legendFormatPrefix = "Total GPU Resources Used:"

[P_NPU_INFO]
name = "NPU Resource Information"
description = ""
unit = "count"
legendFormatPrefix = "NPU Resource Information:"


[P_SVC_kNOW_DOC_COUNT]
name = "Total Recalled Documents"
description = ""
unit = "count"
legendFormatPrefix = "Total Recalled Documents："

[C_SVC_NotALLOCATED]
name = "NotAllocated"
description = ""

[C_SVC_NOTUSED]
name = "NotUsed"
description = ""
