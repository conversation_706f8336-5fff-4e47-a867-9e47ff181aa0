package dao

import (
	"fmt"
	"slices"
	"strconv"

	"github.com/samber/lo"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/models"
)

func clone(tx *gorm.DB) *gorm.DB {
	return tx.Session(&gorm.Session{NewDB: true})
}

func CreateUser(tx *gorm.DB, user *User, passwd string) error {
	user.GenSecret(passwd)
	err := clone(tx).Create(user).Error
	if err != nil {
		return stderr.Wrap(err, "create user")
	}
	if len(user.Roles) > 0 {
		err = clone(tx).Clauses(clause.OnConflict{
			Columns:     []clause.Column{{Name: "name"}},
			Where:       clause.Where{},
			TargetWhere: clause.Where{},
			DoNothing:   true,
		}).Create(&user.Roles).Error
		if err != nil {
			return stderr.Wrap(err, "create user roles")
		}
		for _, r := range user.Roles {
			err = AppendRoleToUser(tx, strconv.FormatInt(int64(user.ID), 10), strconv.FormatInt(int64(r.ID), 10))
			if err != nil {
				return stderr.Wrap(err, "append role to user")
			}
		}
	}
	return nil
}

func CreateOrUpdateUser(tx *gorm.DB, user *User, passwd string) error {
	user.GenSecret(passwd)
	err := clone(tx).Where(User{Name: user.Name}).Assign(user).FirstOrCreate(&user).Error
	if err != nil {
		return stderr.Wrap(err, "update user")
	}
	if len(user.Roles) > 0 {
		err = clone(tx).Clauses(clause.OnConflict{
			Columns:     []clause.Column{{Name: "name"}},
			Where:       clause.Where{},
			TargetWhere: clause.Where{},
			DoNothing:   true,
		}).Create(&user.Roles).Error
		if err != nil {
			return stderr.Wrap(err, "create user roles")
		}
		for _, r := range user.Roles {
			err = AppendRoleToUser(tx, strconv.FormatInt(int64(user.ID), 10), strconv.FormatInt(int64(r.ID), 10))
			if err != nil {
				return stderr.Wrap(err, "append role to user")
			}
		}
	}
	return nil
}

// GetUserByID 因为使用的是 gorm.Find, 因此当用户不存在时, 该方法将返回一个空的 new(User), 而非error or nil
// 建议通过 user.id 是否为空判断用户是否存在
func GetUserByID(tx *gorm.DB, userID string) (*User, error) {
	user := new(User)
	if err := clone(tx).Where("id = ?", userID).Find(user).Error; err != nil {
		return nil, err
	}
	if err := clone(tx).Model(new(Role)).
		Where("id IN (?)",
			clone(tx).Model(new(UserRole)).Select("RoleId").Where("user_id = ?", user.ID)).
		Find(&user.Roles).Error; err != nil {
		return nil, fmt.Errorf("find user roles: %w", err)
	}
	return user, nil
}

// GetUserByName 因为使用的是 gorm.Find, 因此当用户不存在时, 该方法将返回一个空的 new(User), 而非error or nil
// 建议通过 user.id 是否为空判断用户是否存在
func GetUserByName(tx *gorm.DB, name string) (*User, error) {
	user := new(User)
	if err := clone(tx).Where("name = ?", name).Find(user).Error; err != nil {
		return nil, err
	}
	if err := clone(tx).Model(new(Role)).
		Where("id IN (?)",
			clone(tx).Model(new(UserRole)).Select("RoleId").Where("user_id = ?", user.ID)).
		Find(&user.Roles).Error; err != nil {
		return nil, fmt.Errorf("find user roles: %w", err)
	}
	return user, nil
}

func CountUsers(tx *gorm.DB) (int, error) {
	var count int64
	if err := tx.Model(&User{}).Count(&count).Error; err != nil {
		return 0, err
	}
	return int(count), nil
}

func ListUsers(tx *gorm.DB) ([]*User, error) {
	users := make([]*User, 0)
	if err := tx.Find(&users).Error; err != nil {
		return nil, err
	}
	userids := lo.UniqMap(users, func(u *User, _ int) uint { return u.ID })

	roles := make([]*Role, 0)
	if err := clone(tx).Model(new(Role)).
		Where("id IN (?)",
			clone(tx).Model(new(UserRole)).Select("RoleId").Where("user_id IN ?", userids)).
		Find(&roles).Error; err != nil {
		return nil, fmt.Errorf("find roles: %w", err)
	}
	rolesMap := lo.KeyBy(roles, func(r *Role) uint { return r.ID })

	urs := make([]*UserRole, 0)
	if err := clone(tx).Model(new(UserRole)).Where("user_id IN ?", userids).
		Find(&urs).Error; err != nil {
		return nil, fmt.Errorf("find user_roles: %w", err)
	}
	ursMap := lo.GroupBy(urs, func(ur *UserRole) uint { return ur.UserId })
	for _, u := range users {
		for _, r := range ursMap[u.ID] {
			if role, ok := rolesMap[r.RoleId]; ok {
				u.Roles = append(u.Roles, role)
			}
		}
	}
	return users, nil
}

func ListUsersByRoleName(tx *gorm.DB, role string) ([]*User, error) {
	users := make([]*User, 0)
	if err := clone(tx).
		Joins("INNER JOIN user_roles ur ON ur.user_id = users.id").
		Joins("INNER JOIN roles ON ur.role_id = roles.id").
		Where("roles.name = ?", role).Find(&users).Error; err != nil {
		return nil, err
	}
	userids := lo.UniqMap(users, func(u *User, _ int) uint { return u.ID })

	roles := make([]*Role, 0)
	if err := clone(tx).Model(new(Role)).
		Where("id IN (?)",
			clone(tx).Model(new(UserRole)).Select("RoleId").Where("user_id IN ?", userids)).
		Find(&roles).Error; err != nil {
		return nil, fmt.Errorf("find roles: %w", err)
	}
	rolesMap := lo.KeyBy(roles, func(r *Role) uint { return r.ID })

	urs := make([]*UserRole, 0)
	if err := clone(tx).Model(new(UserRole)).Where("user_id IN ?", userids).
		Find(&urs).Error; err != nil {
		return nil, fmt.Errorf("find user_roles: %w", err)
	}
	ursMap := lo.GroupBy(urs, func(ur *UserRole) uint { return ur.UserId })
	for _, u := range users {
		for _, r := range ursMap[u.ID] {
			if role, ok := rolesMap[r.RoleId]; ok {
				u.Roles = append(u.Roles, role)
			}
		}
	}

	return users, nil
}

func DeleteUserByID(tx *gorm.DB, userID string) error {
	if err := clone(tx).Where("id = ?", userID).Delete(&User{}).Error; err != nil {
		return err
	}
	return nil
}

func DeleteUserByName(tx *gorm.DB, name string) error {
	if err := clone(tx).Where("name = ?", name).Delete(&User{}).Error; err != nil {
		return err
	}
	return nil
}

func BatchDeleteUsersByNames(tx *gorm.DB, names []string) error {
	if err := clone(tx).Where("name in (?)", names).Delete(&User{}).Error; err != nil {
		return err
	}
	return nil
}

func AppendRoleToUser(tx *gorm.DB, userID string, roleID string) error {
	uid, _ := strconv.ParseUint(userID, 10, 32)
	rid, _ := strconv.ParseUint(roleID, 10, 32)
	return clone(tx).Clauses(clause.OnConflict{
		DoNothing: true,
	}).Create(&UserRole{
		UserId: uint(uid),
		RoleId: uint(rid),
	}).Error
}

func RemoveRoleFromUser(tx *gorm.DB, userID string, roleID string) error {
	uid, _ := strconv.ParseUint(userID, 10, 32)
	rid, _ := strconv.ParseUint(roleID, 10, 32)
	return clone(tx).Delete(&UserRole{
		UserId: uint(uid),
		RoleId: uint(rid),
	}).Error
}

func UpdateUser(tx *gorm.DB, user *User, passwd string) error {
	user.GenSecret(passwd)
	omits := make([]string, 0)
	if user.Secret == "" {
		omits = append(omits, "secret")
	}
	if user.CreateUser == "" {
		omits = append(omits, "create_user")
	}
	if user.Status == 0 {
		omits = append(omits, "status")
	}
	if user.CreatedAt.IsZero() {
		omits = append(omits, "created_at")
	}
	if user.DeletedAt.Time.IsZero() {
		omits = append(omits, "deleted_at")
	}
	return clone(tx).Select("*").Omit(omits...).Updates(user).Error
}

type User struct {
	gorm.Model

	Name                 string                      `json:"name" gorm:"type:varchar(500);column:name;"`
	Secret               string                      `json:"-" gorm:"type:varchar(255);column:secret;"`
	Roles                []*Role                     `json:"roles" gorm:"-"`
	FullName             string                      `json:"full_name" gorm:"type:varchar(500);column:full_name;"`
	Email                string                      `json:"email" gorm:"type:varchar(500);column:email;"`
	CreateUser           string                      `json:"create_user" gorm:"type:varchar(500);column:create_user;"`
	DefaultProject       string                      `json:"default_project" gorm:"type:varchar(100);column:default_project;"`
	Status               models.UserStatus           `json:"status" gorm:"type:tinyint;column:status;default:1"`
	ExpirationTimeSelect models.ExpirationTimeSelect `json:"expiration_time_select" gorm:"column:expiration_time_select;type:varchar(15);not null;default:nolimit"` // 选择的有效期: 1(24小时),7,15,30,nolimit(无限制),自定义(custom)
	ExpirationTime       int64                       `json:"expiration_time" gorm:"column:expiration_time;type:bigint"`                                             // 秒时间戳 过期时间
	// Groups          []*models.Group `json:"groups" gorm:"many2many:user_group;"`

	WhiteIps    []string `json:"white_ips" gorm:"column:white_ips;type:json;serializer:json"`
	PhoneNumber string   `json:"phone_number" gorm:"column:phone_number;type:varchar(50);not null"`
}

// GenSecret 通过账号密码生成 secret
//
//	@param passwd 密码
func (u *User) GenSecret(passwd string) string {
	// passwd 为空时, 返回不为空的 secret
	if u.Secret != "" && passwd == "" {
		return u.Secret
	}
	if passwd == "" || u.Name == "" {
		return ""
	}
	u.Secret = toolkit.MD5Bytes([]byte(fmt.Sprintf("%s@%s", u.Name, passwd)))
	return u.Secret
}

func (u *User) GetUsername() string {
	return u.Name
}

func (u *User) GetRoles() []string {
	roles := make([]string, len(u.Roles))
	for i, role := range u.Roles {
		roles[i] = role.Name
	}
	return roles
}

// CheckIP 校验ip白名单
//
//	@return bool true: 放行
func (*User) CheckIP(whiteIps []string, clientIP string) bool {
	// 不限制 ip 则返回通过校验
	if len(whiteIps) == 0 {
		return true
	}
	// 严格: 为空拦截
	// 宽松: 为空放行
	if clientIP == "" {
		return !conf.C.Auth.IpWhitelistStrict
	}
	// 客户是否 ip 包含在白名单中
	return slices.Contains(whiteIps, clientIP)
}
