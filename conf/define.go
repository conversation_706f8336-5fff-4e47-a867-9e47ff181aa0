package conf

import (
	"encoding/json"
	"fmt"
	"net/url"
	"os"
	"path"
	"regexp"
	"time"

	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"

	"github.com/mitchellh/mapstructure"
	"github.com/spf13/viper"

	"transwarp.io/applied-ai/aiot/vision-std/conf"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/models/monitor"
)

const (
	EnvPrefix         = "us" // user-service，环境变量前缀 US_
	ConfigTag         = "yaml"
	ConfigName        = "config"
	TenantConfigName  = "tenant"
	AuditConfigName   = "audit"
	MonitorConfigName = "monitor"
)

var C = new(Config)

func init() {
	// NOTE: 环境变量必须可以在静态配置文件中找到对应的配置项，否则不会自动加载
	viper.AutomaticEnv()
	viper.SetEnvPrefix(EnvPrefix)
	viper.SetConfigType(ConfigTag)
	etcPath := os.Getenv("ETC_PATH")
	if etcPath != "" {
		viper.AddConfigPath(etcPath)
	} else {
		viper.AddConfigPath("etc")
	}

	viper.SetConfigName(ConfigName)
	if err := viper.ReadInConfig(); err != nil {
		panic(fmt.Errorf("配置文件 %s 读取失败：%s", ConfigName, err.Error()))
	}
	viper.SetConfigName(TenantConfigName)
	if err := viper.MergeInConfig(); err != nil {
		panic(fmt.Errorf("配置文件 %s 读取失败：%s", TenantConfigName, err.Error()))
	}
	viper.SetConfigName(AuditConfigName)
	if err := viper.MergeInConfig(); err != nil {
		panic(fmt.Errorf("配置文件 %s 读取失败：%s", AuditConfigName, err.Error()))
	}
	viper.SetConfigName(MonitorConfigName)
	if err := viper.MergeInConfig(); err != nil {
		panic(fmt.Errorf("配置文件 %s 读取失败：%s", MonitorConfigName, err.Error()))
	}

	initdata := []byte(viper.GetString("init_data"))
	if len(initdata) > 0 {
		C.InitData = &InitData{}
		err := json.Unmarshal(initdata, C.InitData)
		if err != nil {
			stdlog.Errorf("init_data: %s, unmarshal: %v", initdata, err)
			panic(err)
		}
		viper.Set("init_data", C.InitData)
	}

	if err := viper.Unmarshal(&C, func(dc *mapstructure.DecoderConfig) {
		dc.TagName = ConfigTag
	}); err != nil {
		panic(fmt.Errorf("配置文件 %s 加载失败：%s", ConfigName, err.Error()))
	}
	C.Display()
}

type Config struct {
	Server *ServerConfig `json:"server" yaml:"server"`
	Client *ClientConfig `json:"client" yaml:"client"`
	Auth   *AuthConfig   `json:"auth" yaml:"auth"`

	DB           *DBConfig           `json:"db" yaml:"db"`
	SessionStore *SessionStoreConfig `json:"session_store" yaml:"session_store"`
	Logger       *stdlog.Config      `json:"logger" yaml:"logger"`
	UserStore    *UserStoreConfig    `json:"user_store" yaml:"user_store"`

	Redis *conf.RedisConfig `json:"redis" yaml:"redis"`

	// tdc config
	Tenant *TenantConfig `json:"tenant"`

	InfluxDB conf.InfluxdbConfig `json:"influxdb" yaml:"influxdb"`

	AuditRecord *AuditRecordConfig `json:"audit_record" yaml:"audit_record"`
	HealthInfo  *HealthInfo        `json:"health_info" yaml:"health_info"`
	LoginPage   *LoginPageConfig   `json:"login_page" yaml:"login_page"`
	Monitor     *MonitorConfig     `json:"monitor" yaml:"monitor"`
	Alerting    *AlertingConfig    `json:"alerting" yaml:"alerting"`
	Grafana     *GrafanaConfig     `json:"grafana"`
	Prometheus  *PrometheusConfig  `json:"prometheus" yaml:"prometheus"`
	InitData    *InitData          `json:"init_data" yaml:"init_data"`
}

func (c *Config) Display() {
	bs, _ := json.MarshalIndent(c, "", "    ")
	fmt.Println(string(bs))
}

type ServerConfig struct {
	Name        string `json:"name" yaml:"name"`
	Addr        string `json:"addr" yaml:"addr"`
	CASAddr     string `json:"cas_addr" yaml:"cas_addr"`
	SSLCertPath string `json:"ssl_cert_path" yaml:"ssl_cert_path"`
	SSLKeyPath  string `json:"ssl_key_path" yaml:"ssl_key_path"`
	LiteMode    bool   `json:"lite_mode" yaml:"lite_mode"` // 是否为轻量模式
}

type LoginPageConfig struct {
	TitleZH string `json:"title_zh" yaml:"title_zh"`
	TitleEN string `json:"title_en" yaml:"title_en"`
}

type ClientConfig struct {
	Prefix              string `json:"prefix" yaml:"prefix"`
	MwhSuffix           string `json:"mwh_suffix" yaml:"mwh_suffix"`
	SampleSuffix        string `json:"sample_suffix" yaml:"sample_suffix"`
	MlopsSuffix         string `json:"mlops_suffix" yaml:"mlops_suffix"`
	KnowledgeBaseSuffix string `json:"knowledge_base_suffix" yaml:"knowledge_base_suffix"`
}

type AuthConfig struct {
	Cookie string `json:"cookie" yaml:"cookie"` // CookieName, SOPHONID by default

	Mode   AuthMode      `json:"mode" yaml:"mode"` // cas(default)
	CAS    *CASConfig    `json:"cas" yaml:"cas"`
	OAuth2 *OAuth2Config `json:"oauth2" yaml:"oauth2"`

	PasswordFreeSetCookie bool   `json:"password_free_set_cookie" yaml:"password_free_set_cookie"`
	Token                 string `json:"token" yaml:"token"` // token(default)

	AllowMultiClientLoginAtSameTime bool `json:"allow_multiclient_login_at_same_time" yaml:"allow_multiclient_login_at_same_time"`
	IpWhitelistStrict               bool `json:"ip_whitelist_strict" yaml:"ip_whitelist_strict"` // true: 严格模式
}

type CASConfig struct {
	ServerUrl          string `json:"server_url" yaml:"server_url"`
	EmbeddedServerHost string `json:"embedded_server_host" yaml:"embedded_server_host"`
	// EmbeddedServerExternalHost 为当 LLMOps 内置CAS服务的外部访问方式
	// 可能为域名形式, 也可能为域名+路由前缀的形式
	EmbeddedServerExternalHost string `json:"embedded_server_external_host" yaml:"embedded_server_external_host"`

	TicketValidity *TicketValidityConfig `json:"ticket_validity" yaml:"ticket_validity"`
}

// External 模式下，使用外部的CAS服务进行登录
func (c *CASConfig) External() bool {
	return c.ServerUrl != ""
}

// HandleEmbeddedCasRedirectUrl 将内置CAS服务的默认URL转换为外部可访问的URL(如果配置了域名, 域名+路由前缀, VIP, NAT映射等场景)
func (c *CASConfig) HandleEmbeddedCasRedirectUrl(internalUrl string) (externalUrl string, err error) {
	stdlog.Debugf("get external url for internal url: %s", internalUrl)
	if c.EmbeddedServerExternalHost == "" {
		stdlog.Warnf("no external host found in cas config, use internal url")
		return internalUrl, nil
	}
	internalURL, err := url.Parse(internalUrl)
	if err != nil {
		return "", stderr.Wrap(err, "invalid internal url: %s", internalURL)
	}
	externalURL, err := url.Parse(c.EmbeddedServerExternalHost)
	if err != nil {
		return "", stderr.Wrap(err, "invalid externalHost: %s", externalURL)
	}
	if externalURL.Scheme != "" {
		internalURL.Scheme = externalURL.Scheme
	}
	if externalURL.Host != "" {
		internalURL.Host = externalURL.Host
	}
	if externalURL.Path != "" {
		internalURL.Path = path.Join(externalURL.Path, internalURL.Path)
	}

	return internalURL.String(), nil
}

type TicketValidityConfig struct {
	LoginTicket          time.Duration `json:"login_ticket" yaml:"login_ticket"`
	ServiceTicket        time.Duration `json:"service_ticket" yaml:"service_ticket"`
	TicketGrantingTicket time.Duration `json:"ticket_granting_ticket" yaml:"ticket_granting_ticket"`
}

type DBConfig struct { // User & Role & Permission Meta
	Type string `json:"type" yaml:"type"` // sqlite(default) | mysql

	SQLite *conf.SqliteConfig `json:"sqlite" yaml:"sqlite"`
	MySQL  *conf.MysqlConfig  `json:"mysql" yaml:"mysql"`

	Debug bool `yaml:"debug" yaml:"debug"`
}

type SessionStoreConfig struct { // Token Cache
	Type string `json:"type" yaml:"type"` // memory(default) | redis

	Memory *MemoryConfig `json:"memory" yaml:"memory"`
	// Redis  *conf.RedisConfig `json:"redis" yaml:"redis"`
	// Redis  *RedisConfig  `json:"redis" yaml:"redis"`
}
type MemoryConfig struct {
	ExpireTime time.Duration `json:"expire_time" yaml:"expire_time"`
}
type RedisConfig struct {
	Host       string        `json:"host"`
	Port       string        `json:"port"`
	Password   string        `json:"password"`
	DB         int           `json:"db"`
	ExpireTime time.Duration `json:"expire_time" yaml:"expire_time"`
}

type UserStoreConfig struct {
	Type string `json:"type" yaml:"type"`
}

type TenantConfig struct {
	Strategy     string        `json:"strategy"`
	Hippo        *Hippo        `json:"hippo"`
	DefaultQuota *DefaultQuota `json:"default_quota" yaml:"default_quota"`
	TDC          *TDCConfig    `json:"tdc"`
	TDC5         *TDC5Config   `json:"tdc5"`
	Ingress      *Ingress      `json:"ingress"`
	LlmopsQueue  *LlmopsQueue  `json:"llmops_queue" yaml:"llmops_queue"`
	DocEngine    *DocEngine    `json:"doc_engine" yaml:"doc_engine"`
}

type DocEngine struct {
	Enabled bool `json:"enabled" yaml:"enabled"`
}

type LlmopsQueue struct {
	Enabled bool `json:"enabled" yaml:"enabled"`
}

type Ingress struct {
	BasePath    string `json:"base_path" yaml:"base_path"`
	ServiceName string `json:"service_name" yaml:"service_name"`
	ServicePort int32  `json:"service_port" yaml:"service_port"`
}

type Hippo struct {
	Enabled   bool    `json:"enabled" yaml:"enabled"`
	ChartPath string  `json:"chart_path" yaml:"chart_path"`
	Storage   Storage `json:"storage" yaml:"storage"`
}

type Storage struct {
	Nfs Nfs `json:"nfs" yaml:"nfs"`
}
type Nfs struct {
	Server       string `json:"server" yaml:"server"`
	Path         string `json:"path" yaml:"path"`
	MountOptions string `json:"mountOptions" yaml:"mountOptions"`
}

type DefaultQuota struct {
	LimitsCpu            string `json:"limits_cpu" yaml:"limits_cpu"`
	LimitsMemory         string `json:"limits_memory" yaml:"limits_memory"`
	RequestsStorage      string `json:"requests_storage" yaml:"requests_storage"`
	Pods                 string `json:"pods" yaml:"pods"`
	Gpu                  string `json:"gpu" yaml:"gpu"`
	GpuMemory            string `json:"gpu_memory" yaml:"gpu_memory"`
	Bandwidth            string `json:"bandwidth" yaml:"bandwidth"`
	KnowledgeBaseStorage string `json:"knowledge_base_storage" yaml:"knowledge_base_storage"`
	FileStorage          string `json:"file_storage" yaml:"file_storage"`
}

type TDCConfig struct {
	TenantService       *TenantServiceConfig `json:"tenant_service" yaml:"tenant_service"`
	BrokerService       *BrokerServiceConfig `json:"broker_service" yaml:"broker_service"`
	GuardianAccessToken string               `json:"guardian_access_token" yaml:"guardian_access_token"`
}

type TDC5Config struct {
	TenantService        *Tdc5TenantServiceConfig `json:"tenant_service" yaml:"tenant_service"`
	HippoConfig          *Tdc5HippoConfig         `json:"hippo_config" yaml:"hippo_config"`
	XCloudAccessToken    string                   `json:"x_cloud_access_token" yaml:"x_cloud_access_token"`
	HippoSvcLabels       string                   `json:"hippo_svc_labels" yaml:"hippo_svc_labels"`
	HippoMasterSvcLabels string                   `json:"hippo_master_svc_labels" yaml:"hippo_master_svc_labels"`
}

type TenantServiceConfig struct {
	BaseURL             string `json:"base_url" yaml:"base_url"`
	GuardianAccessToken string `json:"guardian_access_token" yaml:"guardian_access_token"`
	Creator             string `json:"creator" yaml:"creator"`
	Company             string `json:"company" yaml:"company"`
	Department          string `json:"department" yaml:"department"`
	Password            string `json:"password" yaml:"password"`
	UserFullName        string `json:"user_fullname" yaml:"user_fullname"`
	UserEmail           string `json:"user_email" yaml:"user_email"`
}

type Tdc5HippoConfig struct {
	ProdMetaType     string `json:"prod_meta_type" yaml:"prod_meta_type"`
	ProdMetaVersion  string `json:"prod_meta_version" yaml:"prod_meta_version"`
	ProdInstanceName string `json:"prod_instance_name" yaml:"prod_instance_name"`

	ComponentType           string `json:"component_type" yaml:"component_type"`
	ComponentVersion        string `json:"component_version" yaml:"component_version"`
	ComponentEnableKerberos string `json:"component_enable_kerberos" yaml:"component_enable_kerberos"`
	ComponentNetworkKype    string `json:"component_network_type" yaml:"component_network_type"`
}

type Tdc5TenantServiceConfig struct {
	BaseURL             string `json:"base_url" yaml:"base_url"`
	GuardianAccessToken string `json:"guardian_access_token" yaml:"guardian_access_token"`
	Creator             string `json:"creator" yaml:"creator"`
	ClusterID           string `json:"cluster_id" yaml:"cluster_id"`

	CreateTenantPath  string `json:"create_tenant_path" yaml:"create_tenant_path"`
	GetTenantPath     string `json:"get_tenant_path" yaml:"get_tenant_path"`
	GetAllTenantsPath string `json:"get_all_tenants_path" yaml:"get_all_tenants_path"`
}

type BrokerServiceConfig struct {
	BaseURL             string `json:"base_url" yaml:"base_url"`
	GuardianAccessToken string `json:"guardian_access_token" yaml:"guardian_access_token"`
	HippoServiceId      string `json:"hippo_service_id" yaml:"hippo_service_id"`
	HippoTemplateName   string `json:"hippo_template_name" yaml:"hippo_template_name"`
	HippoInstanceName   string `json:"hippo_instance_name" yaml:"hippo_instance_name"`
}

type AuditRecordConfig struct {
	APIs []*AuditRecordAPIConfig `json:"apis" yaml:"apis"`
	// 事件描述 gotemplate渲染时 取值后的格式处理，可以为取值加上样式html标签
	RenderValuePrefix string `json:"render_value_prefix" yaml:"render_value_prefix"`
	RenderValueSuffix string `json:"render_value_suffix" yaml:"render_value_suffix"`
}

type AuditRecordAPIConfig struct {
	Module     string `json:"module" yaml:"module"`
	SubModule  string `json:"sub_module" yaml:"sub_module"`
	OpType     string `json:"op_type" yaml:"op_type"`
	APIModule  string `json:"api_module" yaml:"api_module"`
	APIMethod  string `json:"api_method" yaml:"api_method"`
	APIPath    string `json:"api_path" yaml:"api_path"`
	APIDesc    string `json:"api_desc" yaml:"api_desc"`
	GoTemplate string `json:"go_template" yaml:"go_template"`
	Condition  string `json:"condition" yaml:"condition"`
	Regexp     *regexp.Regexp
}

type SvcMonitorConfig struct {
	DefaultPanels      []string            `json:"default_panels" yaml:"default_panels"`               // source types that not in PanelsBySourceType will use DefaultPanels
	PanelsBySourceType map[string][]string `json:"panels_by_source_type" yaml:"panels_by_source_type"` // service source type -> []panel id,  e.x. SOURCE_TYPE_MODEL_CUBE -> [P_AVG_DURATION, P_AVG_OUTPUT_TOKENS]
}

type AlertingConfig struct {
	DefaultRuleIsPaused *bool  `json:"default_rule_is_paused" yaml:"default_rule_is_paused"`
	ReceiverName        string `json:"receiver_name" yaml:"receiver_name"`
}

type MonitorConfig struct {
	Metrics          []*monitor.Metric `json:"metrics" yaml:"metrics"`
	Panels           []*monitor.Panel  `json:"panels" yaml:"panels"`
	SvcMonitorConfig *SvcMonitorConfig `json:"svc_monitor_config" yaml:"svc_monitor_config"`
	DashboardConfig  *DashboardConfig  `json:"dashboard_config" yaml:"dashboard_config"`
	GlobalConfig     *GlobalConfig     `json:"global_config" yaml:"global_config"`
}

type DashboardConfig struct {
	PanelsByTab map[string][]string `json:"panels_by_tab" yaml:"panels_by_tab"`
}

type GlobalConfig struct {
	GPUOverviewPanels      []string `json:"gpu_overview_panels" yaml:"gpu_overview_panels"`
	GPUTrendPanels         []string `json:"gpu_trend_panels" yaml:"gpu_trend_panels"`
	BaseOverviewPanels     []string `json:"base_overview_panels" yaml:"base_overview_panels"`
	BaseTrendPanels        []string `json:"base_trend_panels" yaml:"base_trend_panels"`
	BaseRankingPanels      []string `json:"base_ranking_panels" yaml:"base_ranking_panels"`
	ServiceRankingPanels   []string `json:"service_ranking_panels" yaml:"service_ranking_panels"`
	GpuResourceUsagePanels []string `json:"gpu_resource_usage_panels" yaml:"gpu_resource_usage_panels"`
	ServiceOverviewPanels  []string `json:"service_overview_panels" yaml:"service_overview_panels"`
	ServiceListPanels      []string `json:"service_list_panels" yaml:"service_list_panels"`
	GpuUsedPanels          []string `json:"gpu_used_panels" yaml:"gpu_used_panels"`
	NpuInfoPanels          []string `json:"npu_info_panels" yaml:"npu_info_panels"`
	ServiceDetailPanels    []string `json:"service_detail_panels" yaml:"service_detail_panels"`
}

type GrafanaConfig struct {
	Host          string `json:"host" yaml:"host"`
	Username      string `json:"username" yaml:"username"`
	Password      string `json:"password" yaml:"password"`
	Debug         bool   `json:"debug" yaml:"debug"`
	DatasourceUid string `json:"datasource_uid" yaml:"datasource_uid"`
	PublicEndpoint string `json:"public_endpoint" yaml:"public_endpoint"` // grafana public dashboard endpoint
}
type PrometheusConfig struct {
	Address string `json:"address" yaml:"address"`
}

type HealthInfo struct {
	AppHealthInfoUrl   string `json:"app_health_info_url" yaml:"app_health_info_url"`
	ModelHealthInfoUrl string `json:"model_health_info_url" yaml:"model_health_info_url"`
}

func (c *AuditRecordAPIConfig) ToPb() *pb.AuditRecordAPIConfig {
	return &pb.AuditRecordAPIConfig{
		Module:     helper.StringToEnum[pb.AuditRecordModule]("AuditRecordModule_"+c.Module, pb.AuditRecordModule_value),
		SubModule:  helper.StringToEnum[pb.AuditRecordSubModule]("AuditRecordSubModule_"+c.SubModule, pb.AuditRecordSubModule_value),
		OpType:     helper.StringToEnum[pb.AuditRecordOperateType](c.OpType, pb.AuditRecordOperateType_value),
		ApiMethod:  c.APIMethod,
		ApiPath:    c.APIPath,
		ApiDesc:    c.APIDesc,
		GoTemplate: c.GoTemplate,
		ApiModule:  c.APIModule,
		Condition:  c.Condition,
	}
}

type InitData struct {
	CreateNamespace bool     `yaml:"create_namespace" json:"create_namespace"`
	Tenants         []Tenant `yaml:"tenants" json:"tenants"`
}

// IsCustom 是否使用配置的自定义数据
func (i *InitData) IsCustom() bool {
	return i != nil && len(i.Tenants) > 0 && i.Tenants[0].ProjectID != "" && i.Tenants[0].TenantUID != ""
}

type Tenant struct {
	ProjectID string `yaml:"project_id" json:"project_id"`
	TenantUID string `yaml:"tenant_uid" json:"tenant_uid"`
}

// IsDevMode 当前是否是开发模式, 如果是的话, 会跳过一些初始化步骤
func IsDevMode() bool {
	return os.Getenv("DEV_SKIP_INIT_DEPS") == "true"
}
