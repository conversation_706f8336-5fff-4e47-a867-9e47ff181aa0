package utils

import (
	"encoding/json"
	"os"
	"strings"

	"github.com/emicklei/go-restful/v3"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/models"
)

const (
	HeaderXFF  = "X-Forwarded-For"
	HeaderXRIP = "X-Real-IP"
)

func IPAddr(host string) string {
	idx := strings.LastIndex(host, ":")
	if idx == -1 {
		return host
	}
	return host[:idx]
}

func MapToJsonStr(param map[string][]string) string {
	dataType, _ := json.Marshal(param)
	jsonStr := string(dataType)
	return jsonStr
}

func JsonStrToMap(str string) map[string][]string {
	var resultMap map[string][]string
	err := json.Unmarshal([]byte(str), &resultMap)
	if err != nil {
		panic(err)
	}
	return resultMap
}

func RemoveValueAndDuplicates(slice []string, valueToRemove string) []string {
	result := []string{}
	seen := make(map[string]bool)

	for _, item := range slice {
		if item != valueToRemove && !seen[item] {
			result = append(result, item)
			seen[item] = true
		}
	}

	return result
}

func RemoveDupElements(arr []string) []string {
	elementMap := make(map[string]bool)
	i := 0
	for _, v := range arr {
		_, ok := elementMap[v]
		if ok {
			continue
		}
		elementMap[v] = true
		arr[i] = v
		i++
	}
	return arr[:i]
}

// RemoveDupProjects 根据projectId删除重复的project
func RemoveDupProjects(projects []*models.Project) []*models.Project {
	elementMap := make(map[string]bool)
	i := 0
	for _, v := range projects {
		_, ok := elementMap[v.ProjectId]
		if ok {
			continue
		}
		elementMap[v.ProjectId] = true
		projects[i] = v
		i++
	}
	return projects[:i]
}

func SliceDifference(slice1 []*models.Permission, slice2 []*models.Permission) []*models.Permission {
	permissions := make([]*models.Permission, 0)
	codeActionMap := map[string]struct{}{}
	for _, permission := range slice2 {
		permissionMapKey := permission.Code + permission.Action + permission.Type
		if _, ok := codeActionMap[permissionMapKey]; !ok {
			codeActionMap[permissionMapKey] = struct{}{}
		}
	}
	for _, permission := range slice1 {
		permissionMapKey := permission.Code + permission.Action + permission.Type
		if _, ok := codeActionMap[permissionMapKey]; !ok {
			permissions = append(permissions, permission)
		}
	}
	return permissions
}

func GetEnvWithDefault(key, defaultValue string) string {
	val, exists := os.LookupEnv(key)
	if !exists {
		return defaultValue
	}

	return val
}

// ClientIP 从 X-Real-IP 中获取客户端 IP
func ClientIP(r *restful.Request) string {
	// 优先尝试从 X-Forwarded-For 头部获取 IP 地址
	// ips := r.Request.Header.Values(HeaderXFF)
	// stdlog.Infof("xff: %+v", ips)
	// if len(ips) > 0 && ips[0] != "" {
	// 	return strings.TrimSpace(ips[0])
	// }
	var ip string
	// XXX 暂不使用, 防止未初始化的 [restful.Request]
	// ip, ok := r.Attribute("client_ip").(string)
	// if ok {
	// 	stdlog.Infof("xrip from attr: %+v", ip)
	// 	return ip
	// }
	// 尝试从 X-Real-IP 头部获取 IP 地址
	ip = strings.TrimSpace(r.Request.Header.Get(HeaderXRIP))
	if ip != "" {
		stdlog.Info("xrip: ", ip)
	}

	// 写入 req 优化同一请求多次获取的操作
	// r.SetAttribute("client_ip", ip)
	return ip
}

// ReplaceLegend replaces the part before the first ':' with newPrefix.
// It keeps the ':' and everything after it intact.
func ReplaceLegend(orig, newPrefix string) string {
	// Find the first colon (ASCII or full-width)
	idx := strings.IndexAny(orig, ":：")
	if idx == -1 {
		return strings.Trim(newPrefix, ":：")
	}
	suffix := orig[idx:]
	if strings.HasSuffix(newPrefix, ":") || strings.HasSuffix(newPrefix, "：") {
		return newPrefix + suffix[1:] // drop the colon from suffix
	}
	return newPrefix + suffix
}
