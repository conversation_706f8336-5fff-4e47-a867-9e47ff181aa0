package migrations

import (
	"github.com/BurntSushi/toml"
	"log"
	"transwarp.io/applied-ai/central-auth-service/models"
)

func initI18n() error {
	models.Trans = make(map[string]map[string]models.TransItem)
	for _, loc := range []struct{ code, file string }{
		{"zh", "./etc/i18n/monitor.zh.toml"},
		{"en", "./etc/i18n/monitor.en.toml"},
	} {
		m := make(map[string]models.TransItem)
		if _, err := toml.DecodeFile(loc.file, &m); err != nil {
			log.Fatalf("failed to load %s: %v", loc.file, err)
		}
		models.Trans[loc.code] = m
	}
	return nil
}
