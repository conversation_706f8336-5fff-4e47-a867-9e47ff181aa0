package flows

import (
	"transwarp.io/applied-ai/central-auth-service/models"
	"transwarp.io/applied-ai/central-auth-service/service/examine/flows/callback"
)

const (
	BaseProjectFlowName = "base_project_flow"
)

var BaseProjectFlow = &models.ExamineFlow{
	Name: BaseProjectFlowName,
	Abbr: "BP",
	Nodes: []*models.ExamineNode{
		{
			Index:     1,
			PrevIndex: 0,
			NextIndex: 2,
		},
		{
			Index:     2,
			PrevIndex: 1,
			NextIndex: 0,
			RoleRequest: []*models.RoleRequest{
				{
					Type:     models.RoleRequestProject,
					RoleName: "空间负责人",
					Name:     "所属空间负责人（或签）",
					NameEn:   "project manager (or sign)",
				},
			},
		},
	},
	Callbacks: []models.FlowFunctionCall{
		{
			Status:   models.Finished,
			Callback: callback.HttpCallback,
		},
		{
			Status:   models.Rejected,
			Callback: callback.RejectHttpCallback,
		},
	},
}
