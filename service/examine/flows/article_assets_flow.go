package flows

import (
	"transwarp.io/applied-ai/central-auth-service/models"
	"transwarp.io/applied-ai/central-auth-service/service/examine/flows/callback"
)

const (
	ArticleAssetsFlowName = "article_assets_flow"
)

var ArticleAssetsFlow = &models.ExamineFlow{
	Name: ArticleAssetsFlowName,
	Abbr: "AA",
	Nodes: []*models.ExamineNode{
		{
			Index:     1,
			PrevIndex: 0,
			NextIndex: 2,
		},
		{
			Index:     2,
			PrevIndex: 1,
			NextIndex: 0,
			RoleRequest: []*models.RoleRequest{
				{
					Type:     models.RoleRequestAssets,
					RoleName: "空间负责人",
					Name:     "公共空间负责人（或签）",
					NameEn:   "public project manager (or sign)",
				},
			},
		},
	},
	Callbacks: []models.FlowFunctionCall{
		{
			Status:   models.Finished,
			Callback: callback.HttpCallback,
		},
	},
}
