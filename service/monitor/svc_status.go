package monitor

type SvcState string

const (
	SvcStateCreating  SvcState = "Creating"  // 上线中
	SvcStatePending   SvcState = "Pending"   // 等待调度
	SvcStateAvailable SvcState = "Available" // 运行中
	SvcStateFailed    SvcState = "Failed"    // 上线失败
	SvcStateOffline   SvcState = "Offline"   // 下线 k8s 中获取不到 待上线 = Offline + 审批状态为空 || Offline + ApprovalInit

	SvcStateApprovalInit     SvcState = "ApprovalInit"     // 未发起审批
	SvcStateUnderApproval    SvcState = "UnderApproval"    // 审批中
	SvcStateApprovalRejected SvcState = "ApprovalRejected" // 审批拒绝
	SvcStateApprovalPassed   SvcState = "ApprovalPassed"   // 审批通过
)

// Enum value maps for SvcState.
var (
	StatusEnumEn = map[int32]string{
		0: "Offline",
		1: "Available",
		2: "Creating",
		3: "Failed",
		4: "Pending",
		5: "ApprovalInit",
		6: "UnderApproval",
		7: "ApprovalRejected",
		8: "ApprovalPassed",
	}
	StatusEnum = map[string]int32{
		"Offline":          0,
		"Available":        1,
		"Creating":         2,
		"Failed":           3,
		"Pending":          4,
		"ApprovalInit":     5,
		"UnderApproval":    6,
		"ApprovalRejected": 7,
		"ApprovalPassed":   8,
	}
	StatusNameCh = map[string]string{
		"Offline":          "下线",
		"Available":        "运行中",
		"Creating":         "上线中",
		"Failed":           "上线失败",
		"Pending":          "等待调度",
		"ApprovalInit":     "未发起审批",
		"UnderApproval":    "审批中",
		"ApprovalRejected": "审批拒绝",
		"ApprovalPassed":   "审批通过",
	}
	StatusEnumCh = map[int32]string{
		0: "下线",
		1: "运行中",
		2: "上线中",
		3: "上线失败",
		4: "等待调度",
		5: "未发起审批",
		6: "审批中",
		7: "审批拒绝",
		8: "审批通过",
	}
)
