package monitor

import (
	"context"
	"fmt"
	"strings"
	"sync"

	utils2 "transwarp.io/applied-ai/aiot/vision-std/toolkit/utils"
	"transwarp.io/applied-ai/central-auth-service/models"
	"transwarp.io/applied-ai/central-auth-service/utils"

	"transwarp.io/aip/llmops-common/pb/serving"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/models/monitor"
	"transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
	"transwarp.io/applied-ai/central-auth-service/service/tenant/util"
)

type MonitorService struct {
	metricsMp           map[string]*monitor.Metric               // metric id -> metric
	panelsMp            map[string]*monitor.Panel                // panel id -> panel
	svcSrcType2Panels   map[serving.SourceType][]*monitor.Panel  // service source type -> []panels
	svcSrcType2Metrics  map[serving.SourceType][]*monitor.Metric // service source type -> []metric
	dashboardTab2Panels map[serving.DashboardTabType][]*monitor.Panel
	global2Panels       map[GlobalPanelType][]*monitor.Panel
	defaultPanels       []*monitor.Panel
	defaultMetrics      []*monitor.Metric
	defaultDatasource   *GrafanaDatasource

	Kc *kubernetes.KClientset
}

var (
	ms     *MonitorService
	msOnce sync.Once
)

func NewMonitorService() *MonitorService {
	msOnce.Do(func() {
		ms = &MonitorService{}
		ms.init()

		kc, _ := kubernetes.NewKClientset()
		ms.Kc = kc
	})
	return ms
}

// ListSvcMetrics 列出服务可用于预警的指标列表
func (ms *MonitorService) ListSvcMetrics(ctx context.Context, req *ListSvcMetricsReq) (*ListMetricsRsp, error) {
	// list service mertrics by service source type
	metrics := ms.listMetricsBySvcSrcType(req.ServiceSourceType)
	// list device metrics
	withGPU := true
	if req.ServiceSourceType == serving.SourceType_SOURCE_TYPE_ROUTER {
		withGPU = false
	}
	metrics = append(metrics, ms.listDeviceMetrics(withGPU)...)
	applyMetricsLocalization(ctx, metrics)
	return &ListMetricsRsp{Metrics: metrics}, nil
}

func (ms *MonitorService) QuerySvcDashboardData(ctx context.Context, req *SvcQueryRequest) (*SvcDashboardData, error) {
	var panels []*monitor.Panel

	if strings.TrimSpace(req.VersionIds) == "" {
		req.VersionIds = ".*"
	}

	if req.DashboardTab == serving.DashboardTabType_DASHBOARD_TAB_TYPE_INVOKE {
		panels = ms.listPanelsBySvcSrcType(req.ServiceSourceType)
	} else if req.DashboardTab == serving.DashboardTabType_DASHBOARD_TAB_TYPE_CUSTOM {
		panels = ms.listPanelsByCustomMetrics(req)
	} else {
		panels = ms.listPanelsByDashboardTab(req.DashboardTab)
	}

	panelsWithData := make([]*PanelWithData, len(panels))

	namespace, err := util.GetCurrentNamespace()
	if err != nil {
		return nil, fmt.Errorf("get current namespace failed: %v", err)
	}

	var qc helper.QueryContext = map[string]any{
		helper.VarServiceId:  req.ServiceId,
		helper.VarStep:       req.StepDuration(),
		helper.VarVersionIds: req.VersionIds,
		helper.VarNamespace:  namespace,
	}

	err = stdsrv.SyncBatchCallGenericWithIdx(panels, func(i int, p *monitor.Panel) error {
		pr := &PanelQueryRequest{QueryRequest: req.QueryRequest, Panel: p}
		data, err := ms.QueryPanelData(ctx, pr, qc)
		if err != nil {
			return err
		}
		panelsWithData[i] = data
		return nil
	})
	if err != nil {
		return nil, err
	}

	p := utils2.DeepCopy(panelsWithData)
	for _, pw := range p {
		applyLocalization(ctx, pw)
	}
	return &SvcDashboardData{ServiceId: req.ServiceId, Panels: p}, nil
}

func (ms *MonitorService) QueryGlobalData(ctx context.Context, req *GlobalBaseQueryRequest) (*SvcDashboardData, error) {
	var panels []*monitor.Panel

	panels = ms.listPanelsByGlobalType(req.GlobalPanelType)

	panelsWithData := make([]*PanelWithData, len(panels))

	var qc helper.QueryContext = map[string]any{
		helper.VarNamespaceName: req.NamespaceName,
		helper.VarNode:          req.Node,
		helper.VarProvider:      req.Provider,
		helper.VarDeviceType:    req.DeviceType,
		helper.VarTopK:          req.TopK,
		helper.VarDeviceUUID:    req.DeviceUUID,
		helper.VarRefName:       req.RefName,
		helper.VarService:       req.Service,
		helper.VarNamespace:     req.Namespace,
		helper.VarServiceId:     req.ServiceId,
	}

	err := stdsrv.SyncBatchCallGenericWithIdx(panels, func(i int, p *monitor.Panel) error {
		pr := &PanelQueryRequest{QueryRequest: req.QueryRequest, Panel: p}
		data, err := ms.QueryPanelData(ctx, pr, qc)
		if err != nil {
			return err
		}
		panelsWithData[i] = data
		return nil
	})
	if err != nil {
		return nil, err
	}
	p := utils2.DeepCopy(panelsWithData)

	for _, pw := range p {
		applyLocalization(ctx, pw)
	}
	return &SvcDashboardData{Panels: p}, nil
}

func applyMetricsLocalization(ctx context.Context, metrics []*monitor.Metric) {
	locale := string(helper.GetLocale(ctx))
	// zh 不做处理
	if len(locale) == 0 || locale == "zh" {
		return
	}
	transMap := models.Trans[locale]

	for _, m := range metrics {
		if ti, ok := transMap[m.Id]; ok {
			m.Name = ti.Name
			m.Description = ti.Description
		}
	}
}

func applyLocalization(ctx context.Context, pw *PanelWithData) {
	locale := string(helper.GetLocale(ctx))

	// zh 不做处理
	if len(locale) == 0 || locale == "zh" {
		return
	}

	transMap := models.Trans[locale]

	// 覆盖 Panel 本身
	if ti, ok := transMap[pw.Panel.Id]; ok {
		pw.Panel.Name = ti.Name
		//pw.Panel.Description = ti.Description
		pw.Panel.Unit = ti.Unit
	}

	// 覆盖每个 metric frame 的 Legend
	for metricID, qd := range pw.Queries {
		if ti, ok := transMap[metricID]; ok && len(ti.LegendFormatPrefix) > 0 {
			for _, frame := range qd.Frames {
				if frame.Legend == "" {
					continue
				}
				frame.Legend = utils.ReplaceLegend(frame.Legend, ti.LegendFormatPrefix)
			}
		}
	}
	for _, metric := range pw.Metrics {
		if ti, ok := transMap[metric.Id]; ok {
			metric.Name = ti.Name
		}
	}

}
func (ms *MonitorService) QueryPanelData(ctx context.Context, r *PanelQueryRequest, qc helper.QueryContext) (*PanelWithData, error) {
	if r == nil {
		return nil, fmt.Errorf("QueryPanelData: PanelQueryRequest is nil")
	}
	if r.Interval == 0 {
		r.Interval = r.Step // default interval is same as step
	}
	mr, err := ms.makePanelMetricRequest(r, qc)
	if err != nil {
		return nil, err
	}
	ret, err := queryMetrics(ctx, mr)
	if err != nil {
		return nil, err
	}
	queriesData := make(map[string]*QueryData)

	for refId, dataResp := range ret {
		qd := new(QueryData)
		qd.Status = int64(dataResp.Status)
		qd.ErrorMsg = dataResp.Error
		if qd.Status != 200 {
			continue
		}
		if len(dataResp.Frames) == 0 {
			qd.Status = 500
			qd.ErrorMsg = "length of Frames is 0"
			continue
		}
		// assert that the length of frame data values is 2, values[0] is times, vales[1] is metric values;
		// ensure that time series from all frames are same, just get time series from frames[0]
		err = dataResp.Frames.AlignByTimes(int64(r.Interval))
		if err != nil {
			return nil, err
		}
		if len(dataResp.Frames[0].Data.Values) > 0 { // check if queried data is not empty
			qd.Times = dataResp.Frames[0].Data.Values[0]
			for _, f := range dataResp.Frames {
				rf := new(Frame)
				s := f.Schema.Fields[1]
				if s.Config != nil && s.Config.DisplayNameFromDS != "" {
					rf.Legend = s.Config.DisplayNameFromDS
				} else {
					rf.Legend = s.Name
				}
				rf.Labels = s.Labels
				rf.Values = f.Data.Values[1]
				qd.Frames = append(qd.Frames, rf)
			}
		}

		queriesData[refId] = qd
	}
	return &PanelWithData{Panel: *r.Panel, Queries: queriesData}, nil
}

// init monitor configs and make caches
func (ms *MonitorService) init() {
	c := conf.C.Monitor

	ms.defaultDatasource = &GrafanaDatasource{Type: DefaultDatasourceType, Uid: conf.C.Grafana.DatasourceUid}

	ms.metricsMp = make(map[string]*monitor.Metric, len(c.Metrics))
	for _, m := range c.Metrics {
		if err := m.Init(); err != nil {
			panic(fmt.Sprintf("init metric [%s] failed: %v", m.Id, err))
		}
		ms.metricsMp[m.Id] = m
	}
	ms.panelsMp = make(map[string]*monitor.Panel, len(c.Panels))
	for _, p := range c.Panels {
		for _, pmr := range p.Metrics {
			if pm, ok := ms.metricsMp[pmr.Id]; !ok {
				panic(fmt.Sprintf("check the monitor config, metric [%s] used by panel [%s] is not found", pmr.Id, p.Id))
			} else {
				pmr.Name = pm.Name
			}
		}
		ms.panelsMp[p.Id] = p
	}

	ms.svcSrcType2Panels = make(map[serving.SourceType][]*monitor.Panel)
	for srcTypeStr, panelIds := range c.SvcMonitorConfig.PanelsBySourceType {
		st := serving.SourceType(serving.SourceType_value[strings.ToUpper(srcTypeStr)])
		panels := make([]*monitor.Panel, 0, len(panelIds))
		for _, panelId := range panelIds {
			p, ok := ms.panelsMp[panelId]
			if !ok {
				panic(fmt.Sprintf("check the monitor config, panel id not found: %s", panelId))
			}
			panels = append(panels, p)
		}
		ms.svcSrcType2Panels[st] = panels
	}

	// init svcSrcType2Metrics by fetching all metrics utilized in the panels corresponding to each source type.
	ms.svcSrcType2Metrics = make(map[serving.SourceType][]*monitor.Metric)
	for st, panels := range ms.svcSrcType2Panels {
		metricIds := make(map[string]struct{})
		for _, p := range panels {
			for _, m := range p.Metrics {
				metricIds[m.Id] = struct{}{}
			}
		}
		metrics := make([]*monitor.Metric, 0, len(metricIds))
		for metricId := range metricIds {
			m, ok := ms.metricsMp[metricId]
			if !ok {
				panic(fmt.Sprintf("check the monitor config, metric id not found: %s", metricId))
			}
			metrics = append(metrics, m)
		}
		ms.svcSrcType2Metrics[st] = metrics
	}

	// init dashboardTab2Panels
	ms.dashboardTab2Panels = make(map[serving.DashboardTabType][]*monitor.Panel)
	for tabStr, panelIds := range c.DashboardConfig.PanelsByTab {
		tab, ok := serving.DashboardTabType_value[strings.ToUpper(tabStr)]
		if !ok {
			panic(fmt.Sprintf("check the monitor config, dashboard tab not found: %s", tabStr))
		}
		panels := make([]*monitor.Panel, 0, len(panelIds))
		for _, panelId := range panelIds {
			p, ok := ms.panelsMp[panelId]
			if !ok {
				panic(fmt.Sprintf("check the monitor config, panel id not found: %s", panelId))
			}
			panels = append(panels, p)
		}
		ms.dashboardTab2Panels[serving.DashboardTabType(tab)] = panels
	}

	ms.defaultPanels = make([]*monitor.Panel, 0, len(c.SvcMonitorConfig.DefaultPanels))
	for _, panelId := range c.SvcMonitorConfig.DefaultPanels {
		p, ok := ms.panelsMp[panelId]
		if !ok {
			panic(fmt.Sprintf("check the monitor config, panel id not found: %s", panelId))
		}
		ms.defaultPanels = append(ms.defaultPanels, p)
	}

	ms.defaultMetrics = make([]*monitor.Metric, 0)
	metricIds := make(map[string]struct{})
	for _, p := range ms.defaultPanels {
		for _, m := range p.Metrics {
			metricIds[m.Id] = struct{}{}
		}
	}

	// init global2Panels.overviewPanels
	ms.convertGlobalPanels(c)

	for mid := range metricIds {
		m, ok := ms.metricsMp[mid]
		if !ok {
			panic(fmt.Sprintf("check the monitor config, metric id not found: %s", mid))
		}
		ms.defaultMetrics = append(ms.defaultMetrics, m)
	}
}

func (ms *MonitorService) convertGlobalPanels(c *conf.MonitorConfig) {
	ms.global2Panels = make(map[GlobalPanelType][]*monitor.Panel)
	if c.GlobalConfig.GPUOverviewPanels != nil {
		panels := make([]*monitor.Panel, 0, len(c.GlobalConfig.GPUOverviewPanels))
		for _, panelId := range c.GlobalConfig.GPUOverviewPanels {
			p, ok := ms.panelsMp[panelId]
			if !ok {
				panic(fmt.Sprintf("check the monitor config, panel id not found: %s", panelId))
			}
			panels = append(panels, p)
		}
		ms.global2Panels[GlobalPanelTypeGpuOverview] = panels
	}

	if c.GlobalConfig.GPUTrendPanels != nil {
		panels := make([]*monitor.Panel, 0, len(c.GlobalConfig.GPUTrendPanels))
		for _, panelId := range c.GlobalConfig.GPUTrendPanels {
			p, ok := ms.panelsMp[panelId]
			if !ok {
				panic(fmt.Sprintf("check the monitor config, panel id not found: %s", panelId))
			}
			panels = append(panels, p)
		}
		ms.global2Panels[GlobalPanelTypeGpuTRENDS] = panels
	}

	if c.GlobalConfig.BaseOverviewPanels != nil {
		panels := make([]*monitor.Panel, 0, len(c.GlobalConfig.BaseOverviewPanels))
		for _, panelId := range c.GlobalConfig.BaseOverviewPanels {
			p, ok := ms.panelsMp[panelId]
			if !ok {
				panic(fmt.Sprintf("check the monitor config, panel id not found: %s", panelId))
			}
			panels = append(panels, p)
		}
		ms.global2Panels[GlobalPanelTypeBaseOverview] = panels
	}

	if c.GlobalConfig.BaseTrendPanels != nil {
		panels := make([]*monitor.Panel, 0, len(c.GlobalConfig.BaseTrendPanels))
		for _, panelId := range c.GlobalConfig.BaseTrendPanels {
			p, ok := ms.panelsMp[panelId]
			if !ok {
				panic(fmt.Sprintf("check the monitor config, panel id not found: %s", panelId))
			}
			panels = append(panels, p)
		}
		ms.global2Panels[GlobalPanelTypeBaseTrend] = panels
	}

	if c.GlobalConfig.BaseRankingPanels != nil {
		panels := make([]*monitor.Panel, 0, len(c.GlobalConfig.BaseRankingPanels))
		for _, panelId := range c.GlobalConfig.BaseRankingPanels {
			p, ok := ms.panelsMp[panelId]
			if !ok {
				panic(fmt.Sprintf("check the monitor config, panel id not found: %s", panelId))
			}
			panels = append(panels, p)
		}
		ms.global2Panels[GlobalPanelTypeBaseRanking] = panels
	}

	if c.GlobalConfig.ServiceRankingPanels != nil {
		panels := make([]*monitor.Panel, 0, len(c.GlobalConfig.ServiceRankingPanels))
		for _, panelId := range c.GlobalConfig.ServiceRankingPanels {
			p, ok := ms.panelsMp[panelId]
			if !ok {
				panic(fmt.Sprintf("check the monitor config, panel id not found: %s", panelId))
			}
			panels = append(panels, p)
		}
		ms.global2Panels[GlobalPanelTypeServiceRanking] = panels
	}

	if c.GlobalConfig.GpuResourceUsagePanels != nil {
		panels := make([]*monitor.Panel, 0, len(c.GlobalConfig.GpuResourceUsagePanels))
		for _, panelId := range c.GlobalConfig.GpuResourceUsagePanels {
			p, ok := ms.panelsMp[panelId]
			if !ok {
				panic(fmt.Sprintf("check the monitor config, panel id not found: %s", panelId))
			}
			panels = append(panels, p)
		}
		ms.global2Panels[GlobalPanelTypeResourceUsage] = panels
	}

	if c.GlobalConfig.ServiceOverviewPanels != nil {
		panels := make([]*monitor.Panel, 0, len(c.GlobalConfig.ServiceOverviewPanels))
		for _, panelId := range c.GlobalConfig.ServiceOverviewPanels {
			p, ok := ms.panelsMp[panelId]
			if !ok {
				panic(fmt.Sprintf("check the monitor config, panel id not found: %s", panelId))
			}
			panels = append(panels, p)
		}
		ms.global2Panels[GlobalPanelTypeSvcOverview] = panels
	}

	if c.GlobalConfig.ServiceListPanels != nil {
		panels := make([]*monitor.Panel, 0, len(c.GlobalConfig.ServiceListPanels))
		for _, panelId := range c.GlobalConfig.ServiceListPanels {
			p, ok := ms.panelsMp[panelId]
			if !ok {
				panic(fmt.Sprintf("check the monitor config, panel id not found: %s", panelId))
			}
			panels = append(panels, p)
		}
		ms.global2Panels[GlobalPanelTypeSvcList] = panels
	}

	if c.GlobalConfig.GpuUsedPanels != nil {
		panels := make([]*monitor.Panel, 0, len(c.GlobalConfig.GpuUsedPanels))
		for _, panelId := range c.GlobalConfig.GpuUsedPanels {
			p, ok := ms.panelsMp[panelId]
			if !ok {
				panic(fmt.Sprintf("check the monitor config, panel id not found: %s", panelId))
			}
			panels = append(panels, p)
		}
		ms.global2Panels[GlobalGpuUsed] = panels
	}

	if c.GlobalConfig.NpuInfoPanels != nil {
		panels := make([]*monitor.Panel, 0, len(c.GlobalConfig.NpuInfoPanels))
		for _, panelId := range c.GlobalConfig.NpuInfoPanels {
			p, ok := ms.panelsMp[panelId]
			if !ok {
				panic(fmt.Sprintf("check the monitor config, panel id not found: %s", panelId))
			}
			panels = append(panels, p)
		}
		ms.global2Panels[GlobalNpuInfo] = panels
	}

	if c.GlobalConfig.ServiceDetailPanels != nil {
		panels := make([]*monitor.Panel, 0, len(c.GlobalConfig.ServiceDetailPanels))
		for _, panelId := range c.GlobalConfig.ServiceDetailPanels {
			p, ok := ms.panelsMp[panelId]
			if !ok {
				panic(fmt.Sprintf("check the monitor config, panel id not found: %s", panelId))
			}
			panels = append(panels, p)
		}
		ms.global2Panels[GlobalPanelTypeSvcDetail] = panels
	}
}

func (ms *MonitorService) listPanelsBySvcSrcType(st serving.SourceType) []*monitor.Panel {
	ret, ok := ms.svcSrcType2Panels[st]
	if !ok {
		ret = ms.defaultPanels
	}
	return ret
}

func (ms *MonitorService) listPanelsByGlobalType(st GlobalPanelType) []*monitor.Panel {
	ret, ok := ms.global2Panels[st]
	if !ok {
		return []*monitor.Panel{}
	}
	return ret
}

func (ms *MonitorService) listMetricsBySvcSrcType(st serving.SourceType) []*monitor.Metric {
	ret, ok := ms.svcSrcType2Metrics[st]
	if !ok {
		ret = ms.defaultMetrics
	}
	return ret
}

func (ms *MonitorService) listDeviceMetrics(withGPU bool) []*monitor.Metric {
	ret := make([]*monitor.Metric, 0)
	var ps []*monitor.Panel
	if withGPU {
		ps = ms.listPanelsByDashboardTab(serving.DashboardTabType_DASHBOARD_TAB_TYPE_DEVICE)
	} else {
		ps = ms.listPanelsByDashboardTab(serving.DashboardTabType_DASHBOARD_TAB_TYPE_DEVICE_WO_GPU)
	}
	for _, p := range ps {
		for _, mr := range p.Metrics {
			if m, ok := ms.metricsMp[mr.Id]; ok && !m.DisableAlerting {
				ret = append(ret, m)
			}
		}
	}
	return ret
}

func (ms *MonitorService) listPanelsByDashboardTab(tab serving.DashboardTabType) []*monitor.Panel {
	ret, ok := ms.dashboardTab2Panels[tab]
	if !ok {
		ret = []*monitor.Panel{}
	}
	return ret
}

// 列出自定义指标的panels
func (ms *MonitorService) listPanelsByCustomMetrics(req *SvcQueryRequest) (ret []*monitor.Panel) {
	customMetricsNames, err := ms.listCustomMetricsNames(context.Background(), req)
	// fmt.Println(customMetricsNames, err)
	if err != nil {
		stdlog.Errorf("listCustomMetricsNames: %v", err)
		return
	}
	for _, n := range customMetricsNames {
		p := &monitor.Panel{
			Id:   n,
			Name: n,
			Metrics: []*monitor.MetricRef{
				{
					Id:   n,
					Name: n,
				},
			},
		}
		ret = append(ret, p)
	}
	return
}
