package monitor

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path"
	"strings"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/clients"
	"transwarp.io/applied-ai/central-auth-service/conf"

	gfpublic "transwarp.io/applied-ai/grafana-openapi-client-go/client/dashboard_public"
	gfdashboard "transwarp.io/applied-ai/grafana-openapi-client-go/client/dashboards"

	gfmodels "transwarp.io/applied-ai/grafana-openapi-client-go/models"
)

const (
	DashboardTmplDir    = "etc/grafana"
	SvcDashboardTmplKey = "svc-dashboard"
)

var (
	DashboardTmplMap map[string]string
)

func init() {
	DashboardTmplMap = make(map[string]string)
	files, err := os.ReadDir(DashboardTmplDir)
	if err != nil {
		panic(err)
	}
	for _, file := range files {
		if !strings.HasSuffix(file.Name(), ".json.tmpl") {
			stdlog.Warnf("skipping non-template file: %s", file.Name())
			continue
		}
		filePath := DashboardTmplDir + "/" + file.Name()
		bs, err := os.ReadFile(filePath)
		if err != nil {
			stdlog.Errorf("failed to read template file %s: %v", filePath, err)
			continue
		}

		entry, _ := strings.CutSuffix(file.Name(), ".json.tmpl")
		DashboardTmplMap[entry] = string(bs)
		stdlog.Infof("loaded dashboard template: %s", entry)
	}
}

func (ms *MonitorService) GetOrCreateSvcDashboard(ctx context.Context, svcId, svcName string) (dashbaordId string, err error) {
	if svcId == "" {
		return "", fmt.Errorf("service ID cannot be empty")
	}
	tmpl, ok := DashboardTmplMap[SvcDashboardTmplKey]
	if !ok {
		return "", fmt.Errorf("dashboard template %s not found", SvcDashboardTmplKey)
	}

	uid := fmt.Sprintf("svc-%s", svcId)
	tmpl = strings.ReplaceAll(tmpl, "replace-this-uid", uid)
	// return if exists
	params := &gfdashboard.GetDashboardByUIDParams{
		UID:     uid,
		Context: ctx,
	}
	if ret, err := clients.GrafanaCli.Dashboards.GetDashboardByUIDWithParams(params, nil); err != nil {
		return "", fmt.Errorf("Dashboards.GetDashboardByUID %s: %v", uid, err)
	} else if ret.Payload.Dashboard != nil {
		return ret.Payload.Dashboard.UID, nil
	}
	if strings.TrimSpace(svcName) == "" {
		tmpl = strings.ReplaceAll(tmpl, "replace-this-title", fmt.Sprintf("服务监控-%s", svcId))
	} else {
		tmpl = strings.ReplaceAll(tmpl, "replace-this-title", fmt.Sprintf("服务监控-%s", svcName))
	}
	tmpl = strings.ReplaceAll(tmpl, "replace-this-service-id", svcId)

	var dashboard gfmodels.JSON
	if err := json.Unmarshal([]byte(tmpl), &dashboard); err != nil {
		return "", fmt.Errorf("unmarshal dashboard template: %v\nContent: %v", err, tmpl)
	}

	importReq := &gfmodels.ImportDashboardRequest{
		Dashboard: dashboard,
		FolderUID: clients.GrafanaFolder,
		Overwrite: true,
		Inputs: []*gfmodels.ImportDashboardInput{
			{
				Name:     "DS_PROMETHEUS",
				PluginID: "prometheus",
				Type:     "datasource",
				Value:    conf.C.Grafana.DatasourceUid,
			},
		},
	}
	ret, err := clients.GrafanaCli.Dashboards.ImportDashboardWithParams(&gfdashboard.ImportDashboardParams{
		Body:    importReq,
		Context: ctx,
	})
	if err != nil {
		return "", fmt.Errorf("failed to import dashboard: %v", err)
	}

	return ret.Payload.UID, nil
}

func (ms *MonitorService) GetOrPublishSvcDashboard(ctx context.Context, svcId, svcName string) (string, error) {
	dashboardUID, err := ms.GetOrCreateSvcDashboard(ctx, svcId, svcName)
	if err != nil {
		return "", fmt.Errorf("GetOrCreateSvcDashboard: %v", err)
	}

	// return if exists
	if ret, err := clients.GrafanaCli.DashboardPublic.GetPublicDashboardWithParams(&gfpublic.GetPublicDashboardParams{
		DashboardUID: dashboardUID,
		Context:      ctx,
	}); err == nil {
		return getPublicDashboardURI(ret.Payload.AccessToken), nil
	}

	params := &gfpublic.CreatePublicDashboardParams{
		Body: &gfmodels.PublicDashboardDTO{
			IsEnabled:            true,
			TimeSelectionEnabled: true,
		},
		DashboardUID: dashboardUID,
		Context:      ctx,
	}
	publishRet, err := clients.GrafanaCli.DashboardPublic.CreatePublicDashboardWithParams(params)
	if err != nil {
		return "", fmt.Errorf("CreatePublicDashboardWithParams: %v", err)
	}

	return getPublicDashboardURI(publishRet.Payload.AccessToken), nil
}

func getPublicDashboardURI(accessToken string) string {
	return path.Join(conf.C.Grafana.PublicEndpoint, accessToken)
}
