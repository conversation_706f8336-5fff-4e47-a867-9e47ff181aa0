package search

import "transwarp.io/aip/llmops-common/pb"

type SearchClient interface {
	Search(searchReq *pb.SearchAssetReq) ([]*pb.SearchAssetResp, error)
}

type AppletClient struct {
	baseURL string
}

func NewAppletClient(baseURL string) *AppletClient {
	return &AppletClient{
		baseURL: baseURL,
	}
}

func (c *AppletClient) Search(searchReq *pb.SearchAssetReq) ([]*pb.SearchAssetResp, error) {
	return nil, nil
}

// model client
type ModelClient struct {
	baseURL string
}

func NewModelClient(baseURL string) *ModelClient {
	return &ModelClient{
		baseURL: baseURL,
	}
}

func (c *ModelClient) Search(searchReq *pb.SearchAssetReq) ([]*pb.SearchAssetResp, error) {
	return nil, nil
}

// corpus client
type CorupsClient struct {
	baseURL string
}

func NewCorupsClient(baseURL string) *CorupsClient {
	return &CorupsClient{
		baseURL: baseURL,
	}
}

func (c *CorupsClient) Search(searchReq *pb.SearchAssetReq) ([]*pb.SearchAssetResp, error) {
	return nil, nil
}

// knowledge base client
type KnowledgeBaseClient struct {
	baseURL string
}

func NewKnowledgeBaseClient(baseURL string) *KnowledgeBaseClient {
	return &KnowledgeBaseClient{
		baseURL: baseURL,
	}
}

func (c *KnowledgeBaseClient) Search(searchReq *pb.SearchAssetReq) ([]*pb.SearchAssetResp, error) {
	return nil, nil
}

// file asset client
type FileAssetClient struct {
	baseURL string
}

func NewFileAssetClient(baseURL string) *FileAssetClient {
	return &FileAssetClient{
		baseURL: baseURL,
	}
}

func (c *FileAssetClient) Search(searchReq *pb.SearchAssetReq) ([]*pb.SearchAssetResp, error) {
	return nil, nil
}
