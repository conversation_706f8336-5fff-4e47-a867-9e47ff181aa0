package search

import (
	"context"

	"transwarp.io/aip/llmops-common/pb"
)

type SearchClient interface {
	Search(searchReq *pb.SearchAssetReq) (*pb.SearchAssetResp, error)
	SearchWithContext(ctx context.Context, searchReq *pb.SearchAssetReq) (*pb.SearchAssetResp, error)
}

type AppletClient struct {
	baseURL string
}

func NewAppletClient(baseURL string) *AppletClient {
	return &AppletClient{
		baseURL: baseURL,
	}
}

func (c *AppletClient) Search(searchReq *pb.SearchAssetReq) (*pb.SearchAssetResp, error) {
	return c.SearchWithContext(context.Background(), searchReq)
}

func (c *AppletClient) SearchWithContext(ctx context.Context, searchReq *pb.SearchAssetReq) (*pb.SearchAssetResp, error) {
	// TODO: Implement actual HTTP call to applet service
	// For now, return empty result to avoid breaking the aggregation
	return &pb.SearchAssetResp{}, nil
}

// model client
type ModelClient struct {
	baseURL string
}

func NewModelClient(baseURL string) *ModelClient {
	return &ModelClient{
		baseURL: baseURL,
	}
}

func (c *ModelClient) Search(searchReq *pb.SearchAssetReq) (*pb.SearchAssetResp, error) {
	return c.SearchWithContext(context.Background(), searchReq)
}

func (c *ModelClient) SearchWithContext(ctx context.Context, searchReq *pb.SearchAssetReq) (*pb.SearchAssetResp, error) {
	// TODO: Implement actual HTTP call to model service
	// For now, return empty result to avoid breaking the aggregation
	return &pb.SearchAssetResp{}, nil
}

// corpus client
type CorupsClient struct {
	baseURL string
}

func NewCorupsClient(baseURL string) *CorupsClient {
	return &CorupsClient{
		baseURL: baseURL,
	}
}

func (c *CorupsClient) Search(searchReq *pb.SearchAssetReq) (*pb.SearchAssetResp, error) {
	return c.SearchWithContext(context.Background(), searchReq)
}

func (c *CorupsClient) SearchWithContext(ctx context.Context, searchReq *pb.SearchAssetReq) (*pb.SearchAssetResp, error) {
	// TODO: Implement actual HTTP call to corpus service
	// For now, return empty result to avoid breaking the aggregation
	return &pb.SearchAssetResp{}, nil
}

// knowledge base client
type KnowledgeBaseClient struct {
	baseURL string
}

func NewKnowledgeBaseClient(baseURL string) *KnowledgeBaseClient {
	return &KnowledgeBaseClient{
		baseURL: baseURL,
	}
}

func (c *KnowledgeBaseClient) Search(searchReq *pb.SearchAssetReq) (*pb.SearchAssetResp, error) {
	return c.SearchWithContext(context.Background(), searchReq)
}

func (c *KnowledgeBaseClient) SearchWithContext(ctx context.Context, searchReq *pb.SearchAssetReq) (*pb.SearchAssetResp, error) {
	// TODO: Implement actual HTTP call to knowledge base service
	// For now, return empty result to avoid breaking the aggregation
	return &pb.SearchAssetResp{}, nil
}

// file asset client
type FileAssetClient struct {
	baseURL string
}

func NewFileAssetClient(baseURL string) *FileAssetClient {
	return &FileAssetClient{
		baseURL: baseURL,
	}
}

func (c *FileAssetClient) Search(searchReq *pb.SearchAssetReq) (*pb.SearchAssetResp, error) {
	return c.SearchWithContext(context.Background(), searchReq)
}

func (c *FileAssetClient) SearchWithContext(ctx context.Context, searchReq *pb.SearchAssetReq) (*pb.SearchAssetResp, error) {
	// TODO: Implement actual HTTP call to file asset service
	// For now, return empty result to avoid breaking the aggregation
	return &pb.SearchAssetResp{}, nil
}
