package search

import (
	"context"
	"sort"
	"sync"
	"time"

	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"

	"transwarp.io/aip/llmops-common/pb"
)

type SearchSerivce struct {
	clients []SearchClient
}

func NewSearchService() *SearchSerivce {
	baseURL := conf.C.Client.Prefix
	clients := []SearchClient{
		NewAppletClient(baseURL),
		NewModelClient(baseURL),
		NewCorupsClient(baseURL),
		NewKnowledgeBaseClient(baseURL),
		NewFileAssetClient(baseURL),
	}

	return &SearchSerivce{
		clients: clients,
	}
}

func (s *SearchSerivce) AggregateSearch(ctx context.Context, searchReq *pb.SearchAssetReq) (*pb.SearchAssetResp, error) {
	stdlog.Infof("search request: %+v", searchReq)

	// Use channels to collect results from concurrent searches
	type searchResult struct {
		items []*pb.SearchAssetRespItem
		err   error
	}

	resultChan := make(chan searchResult, len(s.clients))
	var wg sync.WaitGroup

	// Create a context with timeout for individual search operations
	searchCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	// TODO, call search by assets type
	// if type == "" search all
	if searchReq.Type != pb.SearchAssetType_SEARCH_TYPE_ALL {
		return &pb.SearchAssetResp{}, nil
	}

	// Launch concurrent searches
	for _, client := range s.clients {
		wg.Add(1)
		go func(c SearchClient) {
			defer wg.Done()

			// Perform search with the client using context
			resp, err := c.SearchWithContext(searchCtx, searchReq)
			if err != nil {
				stdlog.Errorf("search client failed: %v", err)
				resultChan <- searchResult{items: nil, err: err}
				return
			}

			resultChan <- searchResult{items: resp.Items, err: nil}
		}(client)
	}

	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// Collect all results
	var allItems []*pb.SearchAssetRespItem

	for result := range resultChan {
		if result.err != nil {
			stdlog.Errorf("search client failed: %v", result.err)
			continue
		}

		if result.items != nil {
			sort.Slice(result.items, func(i, j int) bool {
				return result.items[i].CreatedAt > result.items[j].CreatedAt
			})
			allItems = append(allItems, result.items...)
		}
	}

	page := searchReq.GetPage()
	pageSize := searchReq.GetPageSize()
	total := int32(len(allItems))

	if len(allItems) > 0 {
		pageSize = int32(len(allItems))
	}

	return &pb.SearchAssetResp{
		Total:    total,
		Page:     page,
		PageSize: pageSize,
		Items:    allItems,
	}, nil
}
