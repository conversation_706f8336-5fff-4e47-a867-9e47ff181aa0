package statistics

import (
	"context"
	"fmt"
	"testing"

	"transwarp.io/applied-ai/central-auth-service/models"
)

func TestMain(m *testing.T) {
	queryReq := &models.StaticAssetsQueryReq{
		Page:     1,
		PageSize: 100,
	}

	s := NewStatisticsService(nil, nil)
	result, err := s.GetStaticAssetsDetail(context.Background(), queryReq)
	if err != nil {
		fmt.Println(err.Error())
	}
	fmt.Println(result.Total)
}
