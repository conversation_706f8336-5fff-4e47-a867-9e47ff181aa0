package util

import (
	"fmt"
	"math"
	"math/rand"
	"os"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"k8s.io/apimachinery/pkg/api/resource"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
)

const (
	Zero      = "0"
	ZeroGi    = "0Gi"
	MaxLength = 63
)

func GenerateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyz"
	seededRand := rand.New(rand.NewSource(time.Now().UnixNano()))

	b := make([]byte, length)
	for i := range b {
		b[i] = charset[seededRand.Intn(len(charset))]
	}
	return string(b)
}

func GetCurrentNamespaceOrDefault(defaultName string) string {
	ns, err := GetCurrentNamespace()
	if err != nil {
		stdlog.Errorf("Get current namespace failed, use default %s", defaultName)
		return defaultName
	}
	return ns
}

var GetCurrentNamespace = sync.OnceValues(func() (string, error) {
	if e := os.Getenv("CURRENT_NAMESPACE"); e != "" {
		return e, nil
	}
	nsFile := "/run/secrets/kubernetes.io/serviceaccount/namespace"
	ns, err := os.ReadFile(nsFile)
	if err != nil {
		stdlog.Errorf("Get ns from file %s failed: %v", nsFile, err)
		return "", err
	}
	return strings.TrimSuffix(string(ns), "\n"), err
})

func GetCurrentPodNameOrDefault(defaultName string) string {
	podName := os.Getenv("POD_NAME")
	if podName != "" {
		return podName
	}

	return defaultName
}

func GenNewNamespace(namespace string, appendSuffix bool) (string, bool) {
	currentNs, _ := GetCurrentNamespace()
	newNsName := ""
	if appendSuffix {
		newNsName = fmt.Sprintf("%s%s", namespace, GenerateRandomString(3))
	} else {
		newNsName = fmt.Sprintf("%s-%s", currentNs, namespace)
	}
	return newNsName, validateNamespaceName(newNsName)
}

func validateNamespaceName(namespace string) bool {
	namespaceRegex := regexp.MustCompile(`^[a-z0-9]([-a-z0-9]*[a-z0-9])?$`)

	if len(namespace) < 1 || len(namespace) > 63 {
		return false
	}

	return namespaceRegex.MatchString(namespace)
}

func truncateStr(str string, maxLength int) string {
	if len(str) > maxLength {
		return str[:maxLength]
	}
	return str
}

func GetResourceQuotaName(namespace string) string {
	return truncateStr(fmt.Sprintf("%s-default", namespace), MaxLength)
}

func BytesToGiOrMi(bytes int64) string {
	gi := float64(bytes) / (1 << 30)
	mi := float64(bytes) / (1 << 20)

	if gi == float64(int64(gi)) {
		return fmt.Sprintf("%dGi", int64(gi))
	} else {
		return fmt.Sprintf("%dMi", int64(mi))
	}
}

func BytesToMiWithoutUnit(bytes int64) int64 {
	return bytes / (1 << 20)
}

func GiToGi(gi int64) string {
	return fmt.Sprintf("%dGi", gi)
}

func ToGiFloat(v int64) string {
	gi := float64(v) / (1 << 30)
	return strconv.FormatFloat(gi, 'f', 2, 64) + "Gi"
}

func ToGiInt(v int64) string {
	gi := v / (1 << 30)
	return strconv.FormatInt(gi, 10) + "Gi"
}

func ZeroWithUnit(x string) string {
	if x == "" {
		return "0"
	}

	re := regexp.MustCompile(`^(\d+)(.*)$`)
	matches := re.FindStringSubmatch(x)

	if len(matches) > 1 {
		if len(matches) > 2 && matches[2] != "" {
			return "0" + matches[2]
		}
		return "0"
	}

	return x
}

func ToGiStrCeil(q resource.Quantity) string {
	bytes := q.Value() // int64 bytes
	gi := float64(bytes) / float64(1<<30)
	ceilGi := int64(math.Ceil(gi))
	return fmt.Sprintf("%dGi", ceilGi)
}

func RemoveDuplicatesString(slice []string) []string {
	seen := make(map[string]struct{})
	uniqueSlice := []string{}

	for _, elem := range slice {
		if _, exists := seen[elem]; !exists {
			seen[elem] = struct{}{}
			uniqueSlice = append(uniqueSlice, elem)
		}
	}
	return uniqueSlice
}

func Contains(slice []string, item string) bool {
	for _, elem := range slice {
		if elem == item {
			return true
		}
	}
	return false
}

func CalculateByWeight(str string, weight float64) (string, error) {
	if weight > 1.0 {
		weight = 1.0
	}

	re := regexp.MustCompile(`^(\d+)(.*)$`)
	matches := re.FindStringSubmatch(str)

	if len(matches) > 1 {
		v, _ := strconv.ParseInt(matches[1], 10, 64)
		if len(matches) > 2 && matches[2] != "" {
			res := int64(math.Ceil(float64(v) * weight))
			return fmt.Sprintf("%d%s", res, matches[2]), nil
		}

		res := int64(math.Ceil(float64(v) * weight))
		str := strconv.FormatInt(res, 10)
		return str, nil
	}

	stdlog.Errorf("Invalid quantity quota str: %s", str)
	return str, stderr.Errorf("Invalid quantity quota str")
}
