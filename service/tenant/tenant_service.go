package tenant

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"time"

	"gorm.io/gorm"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/dao"
	"transwarp.io/applied-ai/central-auth-service/models"
	httpclient "transwarp.io/applied-ai/central-auth-service/service/tenant/client"
	"transwarp.io/applied-ai/central-auth-service/service/tenant/consts"
	handler "transwarp.io/applied-ai/central-auth-service/service/tenant/handler"
	helm "transwarp.io/applied-ai/central-auth-service/service/tenant/helm"
	kubernetes "transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
	resourcequota "transwarp.io/applied-ai/central-auth-service/service/tenant/resourcequota"
	customtypes "transwarp.io/applied-ai/central-auth-service/service/tenant/types"
	util "transwarp.io/applied-ai/central-auth-service/service/tenant/util"
)

type TenantService interface {
	ListTenants(ctx context.Context) ([]*models.Tenant, error)
	GetTenant(ctx context.Context, tenantId string, ignoreXpuErr bool) (*models.Tenant, error)
	CreateTenant(ctx context.Context, tenant *models.Tenant, labels map[string]string, ignoreXpuErr bool) (*models.Tenant, error)
	UpdateTenant(ctx context.Context, tenant *models.Tenant, lables map[string]string) (*models.Tenant, error)
	DeleteTenant(ctx context.Context, tenantId string) error

	BatchDeleteTenant(ctx context.Context, tenantIds []string) error

	EnsureLlmopsBasic(tenantUid string) (*models.Instance, error)

	GetHippo(tenantUid string) (*models.Instance, error)
	EnsureHippo(tenantUid string) (*models.Instance, error)
	GetHippoUsedStorage(tenantUid string) (string, error)

	GetResourceQuota(tenantUid string) (*models.TenantResourceQuota, error)
	GetDefaultResourceQuota() *models.TenantResourceQuota

	UpdateResourceQuota(ctx context.Context, ns string, quota *models.ResourceQuotaSpec) (*models.TenantResourceQuota, error)

	ValidateTenantName(ctx context.Context, name string) error
	ValidateTenantUid(ctx context.Context, uid string) error

	CreateTenantIngress(uid string) error

	NxgToResourceQuota(nxgIDs []string) (*models.TenantResourceQuota, error)
}

var kcs *kubernetes.KClientset = nil

func NewTenantService(db *gorm.DB) TenantService {
	client, _ := kubernetes.NewKClientset()

	kcs = client

	switch conf.C.Tenant.Strategy {
	case "k8s":
		return &KubernetesTenantService{db: db, kclient: client}
	case "tdc5":
		tdc5client := httpclient.GetClientFactory().GetTDC5Client()
		return &TDC5TenantService{kclient: client, tdc5client: tdc5client}
	default:
		tdcClient := httpclient.GetClientFactory().GetTDCClient()
		return &TDCTenantService{kclient: client, tdcClient: tdcClient}
	}
}

func getDefaultLabels() map[string]string {
	res := map[string]string{}
	ns, err := util.GetCurrentNamespace()
	if err == nil {
		res[customtypes.NamespaceLableManagedBy] = ns
	}
	return res
}

func getNsLables(labels map[string]string) map[string]string {
	newLables := map[string]string{
		customtypes.NamespaceLableNsType: string(customtypes.TenantNs),
	}
	ns, err := util.GetCurrentNamespace()
	if err == nil {
		newLables[customtypes.NamespaceLableManagedBy] = ns
	}

	// merge labels
	for k, v := range labels {
		newLables[k] = v
	}
	return newLables
}

func validateNamespaceName(namespace string) bool {
	namespaceRegex := regexp.MustCompile(`^[a-z0-9]([-a-z0-9]*[a-z0-9])?$`)

	if len(namespace) < 1 || len(namespace) > 63 {
		return false
	}

	return namespaceRegex.MatchString(namespace)
}

func getResourceQuota(tenantUid string) (*models.TenantResourceQuota, error) {
	// compatible with previous quota, if get quota faild, try to get from k8s ns resourcequota
	manager := resourcequota.MustGetManager()
	q, err := manager.GetQuota(tenantUid)
	if err != nil {
		stdlog.Errorf("Failed to get tenant resource quota: %+v", err)
	} else {
		return q, nil
	}

	attrCh := make(chan []models.Attribute)
	go func() {
		attrCh <- getQuotaItemsAttributes(tenantUid)
	}()
	name := util.GetResourceQuotaName(tenantUid)
	res, err := kcs.K8sInformerClient.GetResourceQuota(tenantUid, name)
	if err != nil {
		return nil, stderr.Errorf(fmt.Sprintf("get ns %s quota failed", tenantUid), err)
	}

	// nvidiaGpu := res.Spec.Hard[customtypes.ResourceNvidiaGpu]
	vGpu := res.Spec.Hard[customtypes.ResourceVGPUCore]

	// nvidiaGpuMemory := res.Spec.Hard[customtypes.ResourceNvidiaGpuMem]
	vGpuMemory := res.Spec.Hard[customtypes.ResourceVGPUMem]

	// usedNvidiaGpu, ok := res.Status.Used[customtypes.ResourceNvidiaGpu]
	usedvGpu := res.Status.Used[customtypes.ResourceVGPUCore]

	// usedNvidiaGpuMemory, ok := res.Status.Used[customtypes.ResourceNvidiaGpuMem]
	usedvGpuMemory := res.Status.Used[customtypes.ResourceVGPUMem]

	bandwidth := res.Spec.Hard[customtypes.ResourceBandwidth]
	usedBandwidth := res.Status.Used[customtypes.ResourceBandwidth]

	fileStorage := res.Spec.Hard[customtypes.ResourceFileStorage]

	knowledgeBaseStorage := res.Spec.Hard[customtypes.ResourceKnowledgeBaseStorage]
	usedKnowledgeBaseStorage := res.Status.Used[customtypes.ResourceKnowledgeBaseStorage]

	limitsCpu := res.Spec.Hard[corev1.ResourceLimitsCPU]
	usedLimitsCpu := res.Status.Used[corev1.ResourceLimitsCPU]

	limitsMemory := res.Spec.Hard[corev1.ResourceLimitsMemory]
	usedLimitsMemory := res.Status.Used[corev1.ResourceLimitsMemory]

	pods := res.Spec.Hard[corev1.ResourcePods]
	usedPods := res.Status.Used[corev1.ResourcePods]

	quota := &models.TenantResourceQuota{
		NameSpace: res.Namespace,
		QuotaName: res.Name,
		Hard: models.ResourceQuotaSpec{
			LimitsCpu:            strconv.FormatInt(limitsCpu.Value(), 10),
			LimitsMemory:         util.ToGiInt(limitsMemory.Value()),
			Pods:                 strconv.FormatInt(pods.Value(), 10),
			Gpu:                  strconv.FormatInt(vGpu.Value(), 10),
			GpuMemory:            util.BytesToGiOrMi(vGpuMemory.Value()),
			Bandwidth:            util.BytesToGiOrMi(bandwidth.Value()),
			KnowledgeBaseStorage: util.ToGiInt(knowledgeBaseStorage.Value()),
			FileStorage:          util.ToGiInt(fileStorage.Value()),
		},
		Used: models.ResourceQuotaSpec{
			LimitsCpu:            strconv.FormatInt(usedLimitsCpu.Value(), 10),
			LimitsMemory:         util.ToGiInt(usedLimitsMemory.Value()),
			Pods:                 strconv.FormatInt(usedPods.Value(), 10),
			Gpu:                  strconv.FormatInt(usedvGpu.Value(), 10),
			GpuMemory:            util.BytesToGiOrMi(usedvGpuMemory.Value()),
			Bandwidth:            util.BytesToGiOrMi(usedBandwidth.Value()),
			KnowledgeBaseStorage: util.ToGiInt(usedKnowledgeBaseStorage.Value()),
			FileStorage:          util.ZeroGi,
		},
		QuotaItemAttributes: <-attrCh,
	}
	if quota.Hard.FileStorage == "0" || quota.Hard.FileStorage == "0.0" || quota.Hard.FileStorage == "0.00" {
		quota.Hard.FileStorage = util.ZeroGi
	}
	if quota.Hard.Knowl == "0" || quota.Hard.Knowl == "0.0" || quota.Hard.Knowl == "0.00" {
		quota.Hard.Knowl = util.ZeroGi
	}
	if quota.Hard.KnowledgeBaseStorage == "0" || quota.Hard.KnowledgeBaseStorage == "0.0" || quota.Hard.KnowledgeBaseStorage == "0.00" {
		quota.Hard.KnowledgeBaseStorage = util.ZeroGi
	}
	return quota, nil
}

func getDefaultResourceQuota() *models.TenantResourceQuota {
	manager := resourcequota.MustGetManager()
	defaultQuota := manager.GetDefaultQuota()
	defaultQuota.Used = models.ResourceQuotaSpec{
		LimitsCpu:    util.Zero,
		LimitsMemory: util.ZeroGi,
		Pods:         util.Zero,
		Gpu:          util.Zero,
		GpuMemory:    util.ZeroGi,
		Bandwidth:    util.ZeroGi,
		Knowl:        util.ZeroGi,
		FileStorage:  util.ZeroGi,
	}
	defaultQuota.QuotaItemAttributes = getQuotaItemsAttributes("")
	return defaultQuota
}

func toKubernetesResourceQuota(ns string, quota *models.ResourceQuotaSpec) (*corev1.ResourceQuota, error) {
	// Create the resource quota
	if quota.Pods == "" {
		quota.Pods = conf.C.Tenant.DefaultQuota.Pods
	}
	pods, err := resource.ParseQuantity(quota.Pods)
	if err != nil {
		stdlog.Errorln(err.Error())
		return nil, err
	}
	limitsCpu, err := resource.ParseQuantity(quota.LimitsCpu)
	if err != nil {
		stdlog.Errorln(err.Error())
		return nil, err
	}
	limitsMemory, err := resource.ParseQuantity(quota.LimitsMemory)
	if err != nil {
		stdlog.Errorln(err.Error())
		return nil, err
	}

	gpuCore, err := resource.ParseQuantity(quota.Gpu)
	if err != nil {
		stdlog.Errorln(err.Error())
		return nil, err
	}
	gpuMem, err := resource.ParseQuantity(conf.C.Tenant.DefaultQuota.GpuMemory)
	if err != nil {
		stdlog.Errorln(err.Error())
		return nil, err
	}
	if quota.GpuMemory != "" {
		vgm, err := resource.ParseQuantity(quota.GpuMemory)
		if err != nil {
			stdlog.Errorln(err.Error())
		} else {
			gpuMem = vgm
		}
	}

	numOfGpu, err := strconv.Atoi(quota.Gpu)
	if err != nil {
		return nil, err
	}
	nvidiaGpu, err := resource.ParseQuantity(strconv.FormatInt(int64(numOfGpu/100), 10))
	if err != nil {
		return nil, err
	}
	// convert to Mi
	gMemInMi := util.BytesToMiWithoutUnit(gpuMem.Value())
	nvidiaGpuMem, err := resource.ParseQuantity(strconv.FormatInt(gMemInMi, 10))
	if err != nil {
		return nil, err
	}

	bandwidth, err := resource.ParseQuantity(quota.Bandwidth)
	if err != nil {
		stdlog.Warnf("bandwith not supply, using default %s", conf.C.Tenant.DefaultQuota.Bandwidth)
		bandwidth, err = resource.ParseQuantity(conf.C.Tenant.DefaultQuota.Bandwidth)
		if err != nil {
			stdlog.Errorln(err.Error())
			return nil, err
		}
	}
	bandwidth = resource.MustParse(util.BytesToGiOrMi(bandwidth.Value()))

	// TODO change from know to KnowledgeBaseStorage
	knowledgeBase, err := resource.ParseQuantity(quota.KnowledgeBaseStorage)
	if err != nil {
		stdlog.Warnf("knowledge base storage not supply, using default %s", conf.C.Tenant.DefaultQuota.KnowledgeBaseStorage)
		knowledgeBase, err = resource.ParseQuantity(conf.C.Tenant.DefaultQuota.KnowledgeBaseStorage)
		if err != nil {
			stdlog.Errorln(err.Error())
			return nil, err
		}
	}
	knowledgeBase = resource.MustParse(util.BytesToGiOrMi(knowledgeBase.Value()))

	storage, err := resource.ParseQuantity(quota.FileStorage)
	if err != nil {
		stdlog.Warnf("file storage not supply, using default %s", conf.C.Tenant.DefaultQuota.FileStorage)
		storage, err = resource.ParseQuantity(conf.C.Tenant.DefaultQuota.FileStorage)
		if err != nil {
			stdlog.Errorln(err.Error())
			return nil, err
		}
	}
	storage = resource.MustParse(util.BytesToGiOrMi(storage.Value()))
	resourceQuota := &corev1.ResourceQuota{
		ObjectMeta: metav1.ObjectMeta{
			Name:      util.GetResourceQuotaName(ns),
			Namespace: ns,
		},
		Spec: corev1.ResourceQuotaSpec{
			Hard: corev1.ResourceList{
				corev1.ResourcePods:                      pods,
				corev1.ResourceLimitsCPU:                 limitsCpu,
				corev1.ResourceLimitsMemory:              limitsMemory,
				corev1.ResourceRequestsCPU:               limitsCpu,
				corev1.ResourceRequestsMemory:            limitsMemory,
				customtypes.ResourceNvidiaGpu:            nvidiaGpu,
				customtypes.ResourceNvidiaGpuMem:         nvidiaGpuMem,
				customtypes.ResourceVGPUCore:             gpuCore,
				customtypes.ResourceVGPUMem:              gpuMem,
				customtypes.ResourceBandwidth:            bandwidth,
				customtypes.ResourceKnowledgeBaseStorage: knowledgeBase,
				customtypes.ResourceFileStorage:          storage,
			},
		},
	}

	return resourceQuota, nil
}

func ensureLlmopsBasic(tenantUid string) (*models.Instance, error) {
	exists, err := helm.LlmopsBasicExists(tenantUid)
	if err != nil {
		stdlog.Errorf("Error checking llmops basic release %s status: %v\n", tenantUid, err)
		return nil, err
	}
	if exists {
		return helm.GetLlmopsBasicRelease(tenantUid)
	}

	quota, _ := getResourceQuota(tenantUid)
	resourceQuota, err := toKubernetesResourceQuota(tenantUid, &quota.Hard)
	if err != nil {
		return nil, err
	}

	params := handler.HandlerParams{
		Namespace:     tenantUid,
		ResourceQuota: resourceQuota,
	}
	handlerChain := handler.NewLlmopsBaiscHandlerChain(tenantUid, kcs)
	err = handlerChain.Handle(context.TODO(), tenantUid, params)
	if err != nil {
		return nil, err
	}

	return helm.GetLlmopsBasicRelease(tenantUid)
}

type QuotaItemName string

const (
	LimitsCpu       QuotaItemName = "limits_cpu"
	LimitsMemory    QuotaItemName = "limits_memory"
	RequestsCpu     QuotaItemName = "requests_cpu"
	RequestsMemory  QuotaItemName = "requests_memory"
	RequestsStorage QuotaItemName = "requests_storage"
	Pods            QuotaItemName = "pods"

	Bandwidth        QuotaItemName = "bandwidth"
	EgressBandwidth  QuotaItemName = "egress_bandwidth"
	IngressBandwidth QuotaItemName = "ingress_bandwidth"

	Gpu       QuotaItemName = "gpu"
	GpuMemory QuotaItemName = "gpu_memory"

	KnowledgeBaseStorage QuotaItemName = "knowledge_base_storage"
	FileStorage          QuotaItemName = "file_storage"
)

func getQuotaItemsAttributes(ns string) []models.Attribute {
	allowCh := make(chan bool, 1)
	go func() {
		if ns != "" {
			currnetNs, err := util.GetCurrentNamespace()
			if err != nil {
				stdlog.Warn("get current namespace: ", err)
			}
			hippoPvcLabelSelector := fmt.Sprintf(consts.K8sHippoPvcLabelSelectorFormat, currnetNs)
			if conf.C.Tenant.Strategy == "tdc" {
				hippoPvcLabelSelector = consts.TdcHippoPvcLabelSelector
			}
			filesSystemStorageLabelSelector := fmt.Sprintf(consts.FileSystemStorageLabelSelectorFormat, currnetNs)

			allowCh <- kcs.AllowVolumeExpansion(ns, filesSystemStorageLabelSelector)
			allowCh <- kcs.AllowVolumeExpansion(ns, hippoPvcLabelSelector)
			return
		}
		allowCh <- true
		allowCh <- true
	}()
	// labelSelector := "transwarp.io/sophon-llmops=true"
	nodes, err := kcs.K8sInformerClient.ListNodes("")
	if err != nil {
		stdlog.Errorf("List nodes err: %+v", err)
		return []models.Attribute{}
	}
	var totalCpu, totalMemory, totalGpu, totalGpuMemory, totalVgpu, totalVgpuMemory resource.Quantity
	for _, node := range nodes {
		cpu := node.Status.Capacity[corev1.ResourceCPU]
		totalCpu.Add(cpu)
		memory := node.Status.Capacity[corev1.ResourceMemory]
		totalMemory.Add(memory)

		gpu := node.Status.Capacity[customtypes.ResourceNvidiaGpu]
		totalGpu.Add(gpu)
		gpuMemory := node.Status.Capacity[customtypes.ResourceNvidiaGpuMem]
		totalGpuMemory.Add(gpuMemory)

		vgpu := node.Status.Capacity[customtypes.ResourceVGPUCore]
		totalVgpu.Add(vgpu)
		vgpuMemory := node.Status.Capacity[customtypes.ResourceVGPUMem]
		totalVgpuMemory.Add(vgpuMemory)
	}

	fileStorageAllowExpansion, knowledgeBaseAllowExpansion := <-allowCh, <-allowCh

	return []models.Attribute{
		{
			Name:           string(LimitsCpu),
			AllowExpansion: true,
			Limit:          totalCpu.String(),
		},
		{
			Name:           string(LimitsMemory),
			AllowExpansion: true,
			Limit:          util.ToGiInt(totalMemory.Value()),
		},
		{
			Name:           string(Bandwidth),
			AllowExpansion: true,
			Limit:          "1024Mi",
		},
		{
			Name:           string(Gpu),
			AllowExpansion: true,
			// ignore for poc
			// Limit:          strconv.FormatInt(totalVgpu.Value(), 10),
			Limit: "100000",
		},
		{
			Name:           string(GpuMemory),
			AllowExpansion: true,
			// ignore for poc
			// Limit:          util.ToGiInt(totalVgpuMemory.Value()),
			Limit: "100000Gi",
		},
		{
			Name:           string(KnowledgeBaseStorage),
			AllowExpansion: knowledgeBaseAllowExpansion,
			Limit:          "10240000Gi",
		},
		{
			Name:           string(FileStorage),
			AllowExpansion: fileStorageAllowExpansion,
			Limit:          "10240000Gi",
		},
	}
}

func InitTenant(ctx context.Context, projectId string, tensvc TenantService) (string, error) {
	proj := &models.Project{
		ProjectId: projectId,
	}
	ptMap := make(map[string]string, len(conf.C.InitData.Tenants))
	if conf.C.InitData.IsCustom() {
		for _, t := range conf.C.InitData.Tenants {
			ptMap[t.ProjectID] = t.TenantUID
		}
	}
	defQuota := tensvc.GetDefaultResourceQuota()
	labels := map[string]string{
		customtypes.NamespaceLableNsType: string(customtypes.SystemNs),
	}
	var created *models.Tenant
	err := dao.DB.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		err := tx.Model(proj).
			Where("project_id = ?", projectId).
			Where("tenant_uid = '' or tenant_uid is null").
			Take(proj).Error
		if err != nil {
			return fmt.Errorf("find project with empty tenant_uid: %w", err)
		}
		if conf.C.InitData.IsCustom() {
			proj.TenantUid = ptMap[proj.ProjectId]
		} else {
			proj.TenantUid = proj.ProjectId
		}
		// 允许 expense http 调用失败
		created, err = tensvc.CreateTenant(ctx,
			&models.Tenant{
				TenantName:   proj.Name,
				TenantUid:    proj.TenantUid,
				TenantLogo:   proj.Logo,
				TenantQuotas: *defQuota,
				Creator:      proj.CreateUser,
				CreateTime:   uint64(time.Now().Unix()),
			}, labels, true)
		if err != nil {
			return fmt.Errorf("create tenant %s: %s: %w", proj.Name, proj.TenantUid, err)
		}
		err = tx.Model(proj).Update("TenantUid", created.TenantUid).Error
		if err != nil {
			return fmt.Errorf("update project TenantUid %s: %w", proj.Name, err)
		}
		return nil
	})
	if err != nil {
		return "", err
	}
	return created.TenantUid, nil
}

func nxgToResourceQuota(nxgIDs []string) (*models.TenantResourceQuota, error) {
	manager := resourcequota.MustGetManager()
	spec, err := manager.NxgToResourceQuotaSpec(nxgIDs)
	if err != nil {
		stdlog.Errorf("Failed to convert nxg to resource quota: %+v", err)
	}

	return &models.TenantResourceQuota{
		Hard: *spec,
	}, nil
}
