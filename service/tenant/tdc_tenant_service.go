package tenant

import (
	"context"
	"fmt"
	"time"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/models"
	httpclient "transwarp.io/applied-ai/central-auth-service/service/tenant/client"
	handler "transwarp.io/applied-ai/central-auth-service/service/tenant/handler"
	kubernetes "transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
	customtypes "transwarp.io/applied-ai/central-auth-service/service/tenant/types"
	util "transwarp.io/applied-ai/central-auth-service/service/tenant/util"
)

type TDCTenantService struct {
	kclient   *kubernetes.KClientset
	tdcClient *httpclient.TDCClient
}

// tdc tenant implatecation
func (t *TDCTenantService) ListTenants(ctx context.Context) ([]*models.Tenant, error) {
	return t.tdcClient.ListTenants(ctx)
}

func (t *TDCTenantService) GetTenant(ctx context.Context, tenantId string, ignoreXpuErr bool) (*models.Tenant, error) {
	tenant, err := t.tdcClient.GetTenant(ctx, tenantId)
	if err != nil {
		stdlog.Errorf("Get tdc tenant %s failed, err: %+v", tenantId, err)
		return nil, err
	}

	hippoSvc, err := httpclient.GetHippoServiceName(tenantId)
	if err != nil {
		stdlog.Errorf("Get hippo svc name failed: %+v", err)
	}
	tenant.HippoServiceName = hippoSvc
	//// 注入注入资源组信息
	//resourceGroups, err := expense.ListNodeXPUGroup(ctx, &expense.ListXPUGroupParam{Tenants: []string{tenantId}})
	//if err != nil {
	//	return nil, err
	//}
	//tenantRsMap := expense.GetTenantResourceGroupMap(resourceGroups)
	//if v, ok := tenantRsMap[tenantId]; ok {
	//	tenant.ResourceGroupDetails = expense.BatchCvtToResourceGroupTenant(v, tenantId)
	//}
	//// 注入能否删除
	//tenant.CanDelete = tenant.DeleteSafe(true)

	rq, err := t.GetResourceQuota(tenantId)
	if err != nil {
		stdlog.Errorf("Get tenant quota failed: %+v", err)
	} else if rq != nil {
		tenant.TenantQuotas = *rq
	}

	return tenant, nil
}

func (t *TDCTenantService) CreateTenant(ctx context.Context, tenant *models.Tenant, labels map[string]string, ignoreXpuErr bool) (*models.Tenant, error) {
	finalTenantUid, _ := util.GenNewNamespace(tenant.TenantUid, true)
	tenant.TenantUid = finalTenantUid

	t1, _ := t.GetTenant(ctx, tenant.TenantUid, ignoreXpuErr)
	if t1 != nil {
		return nil, stderr.Errorf(fmt.Sprintf("tenant %s already exists", tenant.TenantUid))
	}

	err := t.tdcClient.CreateTenant(context.Background(), tenant)
	if err != nil {
		return nil, err
	}

	rq := &tenant.TenantQuotas
	if rq.Hard.LimitsCpu == "" && rq.Hard.LimitsMemory == "" {
		rq = getDefaultResourceQuota()
	}

	quota, _ := toKubernetesResourceQuota(tenant.TenantUid, &rq.Hard)
	annotations := make(map[string]string, 0)
	annotations[customtypes.TenantUidKey] = tenant.TenantUid
	annotations[customtypes.NewSizeKey] = tenant.TenantQuotas.Hard.FileStorage

	//if len(tenant.ResourceGroupIDs) > 0 {
	//	if err := expense.BindingXPUGroupToTenant(ctx, &expense.BindingXPUGroupParam{
	//		XPUGroup: tenant.ResourceGroupIDs,
	//		TenantID: tenant.TenantUid,
	//	}); err != nil {
	//		return nil, err
	//	}
	//}

	quota.Annotations = annotations

	params := handler.HandlerParams{
		Namespace:           tenant.TenantUid,
		NamespaceLables:     getNsLables(labels),
		ResourceQuota:       quota,
		TenantResourceQuota: rq,
	}
	initHandlerChain := handler.NewTdcInitHandlerChain(tenant.TenantUid, t.kclient)
	//  async exec init tenant
	go initHandlerChain.Handle(context.Background(), tenant.TenantUid, params)

	return t.GetTenant(ctx, tenant.TenantUid, ignoreXpuErr)
}

func (t *TDCTenantService) UpdateTenant(ctx context.Context, tenant *models.Tenant, labels map[string]string) (*models.Tenant, error) {
	//if len(tenant.ResourceGroupIDs) > 0 {
	//	if err := expense.BindingXPUGroupToTenant(ctx, &expense.BindingXPUGroupParam{
	//		XPUGroup: tenant.ResourceGroupIDs,
	//		TenantID: tenant.TenantUid,
	//	}); err != nil {
	//		return nil, err
	//	}
	//}
	rq := &tenant.TenantQuotas
	resourceQuota, err := toKubernetesResourceQuota(tenant.TenantUid, &rq.Hard)
	if err != nil {
		return nil, err
	}
	annotations := make(map[string]string, 0)
	annotations[customtypes.TenantUidKey] = tenant.TenantUid
	annotations[customtypes.NewSizeKey] = tenant.TenantQuotas.Hard.FileStorage

	resourceQuota.Annotations = annotations

	params := handler.HandlerParams{
		Namespace:       tenant.TenantUid,
		NamespaceLables: getNsLables(labels),
		ResourceQuota:   resourceQuota,
	}
	updateHandlerChain := handler.NewTdcUpdateHandlerChain(tenant.TenantUid, t.kclient)
	//  async exec update tenant
	go updateHandlerChain.Handle(ctx, tenant.TenantUid, params)

	return tenant, nil
}

// TODO tdc no api to detete tenant, because delete tenant is very dangerous
// delete tenant can only on tdc manager with double check
func (t *TDCTenantService) DeleteTenant(ctx context.Context, tenantId string) error {
	handlerChain := handler.NewTdcDeleteHandlerChain(tenantId, t.kclient)
	err := handlerChain.Handle(context.Background(), tenantId, handler.HandlerParams{})
	if err != nil {
		stdlog.Errorf("Exec uninstall handler chain failed: %+v", err)
		return err
	}
	// TODO call tdc api delete tenant
	return nil
}

func (t *TDCTenantService) BatchDeleteTenant(ctx context.Context, tenantIds []string) error {
	for _, tenant := range tenantIds {
		if err := t.DeleteTenant(ctx, tenant); err != nil {
			return err
		}
	}
	return nil
}

func (t *TDCTenantService) EnsureLlmopsBasic(tenantUid string) (*models.Instance, error) {
	return ensureLlmopsBasic(tenantUid)
}

func (t *TDCTenantService) EnsureHippo(tenantUid string) (*models.Instance, error) {
	installed, err := t.tdcClient.IsHippoInstalled(context.Background(), tenantUid)
	if err != nil {
		return nil, err
	}
	if installed {
		return t.tdcClient.GetHippo(context.Background(), tenantUid)
	}

	quota, err := t.GetResourceQuota(tenantUid)
	if err != nil {
		stdlog.Errorf("Get %s's resource quota err: %+v", tenantUid, err)
		return nil, err
	}
	resourceQuota, err := toKubernetesResourceQuota(tenantUid, &quota.Hard)
	if err != nil {
		stdlog.Errorf("Transform To k8s resource quota err: %+v", err)
		return nil, err
	}

	initHippoHandlerChain := handler.NewTdcHippoInitHandlerChain(tenantUid, t.kclient)
	params := handler.HandlerParams{
		Namespace:     tenantUid,
		ResourceQuota: resourceQuota,
	}
	go initHippoHandlerChain.Handle(context.Background(), tenantUid, params)

	waitTime := 0
	for waitTime < 10 {
		installed, err := t.tdcClient.IsHippoInstalled(context.Background(), tenantUid)
		if err != nil {
			return nil, err
		} else if installed {
			break
		} else {
			waitTime = waitTime + 1
			time.Sleep(1 * time.Second)
		}
	}
	return t.GetHippo(tenantUid)
}

func (t *TDCTenantService) GetHippo(tenantUid string) (*models.Instance, error) {
	instance, err := t.tdcClient.GetHippo(context.Background(), tenantUid)
	if err != nil {
		stdlog.Errorf("Get %s's hippo failed: %+v", tenantUid, err)
		return nil, err
	}
	return instance, nil
}

func (t *TDCTenantService) GetResourceQuota(tenantUid string) (*models.TenantResourceQuota, error) {
	quota, err := getResourceQuota(tenantUid)
	if err != nil {
		return nil, err
	}
	hippoUsedStorage, err := t.GetHippoUsedStorage(tenantUid)
	if err != nil {
		stdlog.Errorf("Get hippo used storage failed, error: %s.", err.Error())
	}
	quota.Used.Knowl = hippoUsedStorage
	quota.Used.KnowledgeBaseStorage = hippoUsedStorage

	return quota, nil
}

func (t *TDCTenantService) GetDefaultResourceQuota() *models.TenantResourceQuota {
	return getDefaultResourceQuota()
}

func (t *TDCTenantService) UpdateResourceQuota(ctx context.Context, ns string, quota *models.ResourceQuotaSpec) (*models.TenantResourceQuota, error) {
	resourceQuota, err := toKubernetesResourceQuota(ns, quota)
	if err != nil {
		return nil, err
	}
	annotations := make(map[string]string, 0)
	annotations[customtypes.TenantUidKey] = ns
	annotations[customtypes.NewSizeKey] = quota.FileStorage

	resourceQuota.Annotations = annotations

	params := handler.HandlerParams{
		Namespace:     ns,
		ResourceQuota: resourceQuota,
	}
	updateHandlerChain := handler.NewTdcUpdateHandlerChain(ns, t.kclient)
	err = updateHandlerChain.Handle(ctx, ns, params)
	if err != nil {
		return nil, err
	}

	return t.GetResourceQuota(ns)
}

func (t *TDCTenantService) ValidateTenantName(ctx context.Context, name string) error {
	tenants, err := t.ListTenants(ctx)
	if err != nil {
		return err
	}
	for _, tenant := range tenants {
		if tenant.TenantName == name {
			return stderr.Error(fmt.Sprintf("tenant name %s exists", name))
		}
	}
	return nil
}

func (t *TDCTenantService) ValidateTenantUid(ctx context.Context, uid string) error {
	newNsName, valid := util.GenNewNamespace(uid, true)
	if !valid || len(newNsName) > 20 {
		return stderr.Error(fmt.Sprintf(`Invalid tenant id %s, 1. can only contain lowercase alphanumeric characters (a-z, 0-9) and hyphens (-);
		2. must start and end with an alphanumeric character; 3. must be between 1 and 20 characters.`, newNsName))
	}

	tenants, err := t.ListTenants(ctx)
	if err != nil {
		return err
	}
	for _, tenant := range tenants {
		if tenant.TenantName == newNsName {
			return stderr.Error(fmt.Sprintf("tenant uid %s exsits", newNsName))
		}
	}
	return nil
}

func (t *TDCTenantService) GetHippoUsedStorage(tenantUid string) (string, error) {
	usedstorage, err := httpclient.GetHippoUsedStorage(tenantUid)
	return fmt.Sprintf("%dGi", usedstorage), err
}

func (t *TDCTenantService) CreateTenantIngress(uid string) error {
	hc := handler.NewIngressInitHandlerChain(uid, t.kclient)
	return hc.Handle(context.TODO(), uid, handler.HandlerParams{
		Namespace: uid,
	})
}

func (t *TDCTenantService) NxgToResourceQuota(nxgIDs []string) (*models.TenantResourceQuota, error) {
	return nxgToResourceQuota(nxgIDs)
}
