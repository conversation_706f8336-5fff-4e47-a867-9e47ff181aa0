package tenant

import (
	"context"
	"fmt"
	"time"

	"transwarp.io/aip/llmops-common/pkg/expense"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/models"
	httpclient "transwarp.io/applied-ai/central-auth-service/service/tenant/client"
	handler "transwarp.io/applied-ai/central-auth-service/service/tenant/handler"
	kubernetes "transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
	customtypes "transwarp.io/applied-ai/central-auth-service/service/tenant/types"
	"transwarp.io/applied-ai/central-auth-service/service/tenant/util"
)

type TDC5TenantService struct {
	kclient    *kubernetes.KClientset
	tdc5client *httpclient.TDC5Client
}

// TODO
func (tdc5 *TDC5TenantService) ListTenants(ctx context.Context) ([]*models.Tenant, error) {
	tenants, err := tdc5.tdc5client.ListTenants(ctx)
	if err != nil {
		return nil, err
	}
	// 注入注入资源组信息
	resourceGroups, err := expense.ListNodeXPUGroup(ctx, &expense.ListXPUGroupParam{Tenants: []string{}})
	if err != nil {
		return nil, err
	}
	tenantRsMap := expense.GetTenantResourceGroupMap(resourceGroups)
	for _, t := range tenants {
		if v, ok := tenantRsMap[t.TenantUid]; ok {
			t.ResourceGroupDetails = expense.BatchCvtToResourceGroupTenant(v, t.TenantUid)
		}
	}
	// 注入能否删除
	for _, t := range tenants {
		t.CanDelete = false
	}
	return tenants, nil
}

func (tdc5 *TDC5TenantService) GetTenant(ctx context.Context, tenantId string, ignoreXpuErr bool) (*models.Tenant, error) {
	tenant, err := tdc5.tdc5client.GetTenant(ctx, tenantId)
	if err != nil {
		stdlog.Errorf("Get tdc tenant %s failed, err: %+v", tenantId, err)
		return nil, err
	}

	hippoSvc, err := httpclient.GetHippoServiceName(tenant.ClusterNamespace)
	if err != nil {
		stdlog.Errorf("Get hippo svc name failed: %+v", err)
	}
	tenant.HippoServiceName = hippoSvc
	// 注入能否删除
	tenant.CanDelete = false
	// 注入注入资源组信息
	resourceGroups, err := expense.ListNodeXPUGroup(ctx, &expense.ListXPUGroupParam{Tenants: []string{tenantId}})
	if err != nil {
		stdlog.Errorf("List node xpu group: %s", err)
		if !ignoreXpuErr {
			return nil, err
		}
	}
	tenantRsMap := expense.GetTenantResourceGroupMap(resourceGroups)
	if v, ok := tenantRsMap[tenantId]; ok {
		tenant.ResourceGroupDetails = expense.BatchCvtToResourceGroupTenant(v, tenantId)
	}
	rq, err := tdc5.GetResourceQuota(tenantId)
	if err != nil {
		stdlog.Errorf("Get tenant quota failed: %+v", err)
	} else if rq != nil {
		tenant.TenantQuotas = *rq
	}
	return tenant, nil
}

func (tdc5 *TDC5TenantService) CreateTenant(ctx context.Context, tenant *models.Tenant, labels map[string]string, ignoreXpuErr bool) (*models.Tenant, error) {
	finalTenantUid, _ := util.GenNewNamespace(tenant.TenantUid, true)
	tenant.TenantUid = finalTenantUid

	t1, _ := tdc5.GetTenant(ctx, tenant.TenantUid, ignoreXpuErr)
	if t1 != nil {
		return nil, stderr.Errorf(fmt.Sprintf("tenant %s already exists", tenant.TenantUid))
	}

	err := tdc5.tdc5client.CreateTenant(context.Background(), tenant, getNsLables(labels))
	// err := tdc5.tdc5client.CreateTenant(&tdc5TenantReqBody)
	if err != nil {
		return nil, err
	}

	rq := &tenant.TenantQuotas
	if rq.Hard.LimitsCpu == "" && rq.Hard.LimitsMemory == "" {
		rq = getDefaultResourceQuota()
	}

	quota, _ := toKubernetesResourceQuota(tenant.TenantUid, &rq.Hard)
	annotations := make(map[string]string, 0)
	annotations[customtypes.TenantUidKey] = tenant.TenantUid
	annotations[customtypes.NewSizeKey] = tenant.TenantQuotas.Hard.FileStorage

	quota.Annotations = annotations

	if len(tenant.ResourceGroupIDs) > 0 {
		if err := expense.BindingXPUGroupToTenant(ctx, &expense.BindingXPUGroupParam{
			XPUGroup: tenant.ResourceGroupIDs,
			TenantID: tenant.TenantUid,
		}); err != nil {
			stdlog.Errorf("BindingXPUGroupToTeant failed: %v", err)
			if !ignoreXpuErr {
				return nil, err
			}
		}
	}
	params := handler.HandlerParams{
		Namespace:           tenant.TenantUid,
		NamespaceLables:     getNsLables(labels),
		ResourceQuota:       quota,
		TenantResourceQuota: rq,
	}
	initHandlerChain := handler.NewTdc5InitHandlerChain(tenant.TenantUid, tdc5.kclient)
	//  async exec init tenant
	go initHandlerChain.Handle(context.Background(), tenant.TenantUid, params)

	return tdc5.GetTenant(ctx, tenant.TenantUid, ignoreXpuErr)
}

func (tdc5 *TDC5TenantService) UpdateTenant(ctx context.Context, tenant *models.Tenant, labels map[string]string) (*models.Tenant, error) {
	if len(tenant.ResourceGroupIDs) > 0 {
		if err := expense.BindingXPUGroupToTenant(ctx, &expense.BindingXPUGroupParam{
			XPUGroup: tenant.ResourceGroupIDs,
			TenantID: tenant.TenantUid,
		}); err != nil {
			return nil, err
		}
	}

	rq := &tenant.TenantQuotas
	resourceQuota, err := toKubernetesResourceQuota(tenant.TenantUid, &rq.Hard)
	if err != nil {
		return nil, err
	}
	annotations := make(map[string]string, 0)
	annotations[customtypes.TenantUidKey] = tenant.TenantUid
	annotations[customtypes.NewSizeKey] = tenant.TenantQuotas.Hard.FileStorage

	resourceQuota.Annotations = annotations

	params := handler.HandlerParams{
		Namespace:           tenant.TenantUid,
		NamespaceLables:     getNsLables(labels),
		ResourceQuota:       resourceQuota,
		TenantResourceQuota: &tenant.TenantQuotas,
	}
	updateHandlerChain := handler.NewTdc5UpdateHandlerChain(tenant.TenantUid, tdc5.kclient)
	//  async exec update tenant
	go updateHandlerChain.Handle(ctx, tenant.TenantUid, params)

	return tenant, nil
}

func (tdc5 *TDC5TenantService) DeleteTenant(ctx context.Context, tenantId string) error {
	return nil
}

func (tdc5 *TDC5TenantService) BatchDeleteTenant(ctx context.Context, tenantIds []string) error {
	return nil
}

func (tdc5 *TDC5TenantService) EnsureLlmopsBasic(tenantUid string) (*models.Instance, error) {
	return ensureLlmopsBasic(tenantUid)
}

// TODO
func (tdc5 *TDC5TenantService) GetHippo(tenantUid string) (*models.Instance, error) {
	// return helm.GetHippoRelease(tenantUid)
	instance := &models.Instance{
		Name:   "llmops-hippo",
		Id:     "llmops-hippo",
		Status: "UNHEALTHY",
	}
	running := httpclient.IsHippoRunning(tenantUid)
	if running {
		instance.Status = "RUNNING"
	}
	return instance, nil
}

func (tdc5 *TDC5TenantService) EnsureHippo(tenantUid string) (*models.Instance, error) {
	installed, err := tdc5.tdc5client.IsHippoInstalled(context.Background(), tenantUid)
	if err != nil {
		return nil, err
	}
	if installed {
		return tdc5.GetHippo(tenantUid)
	}

	quota, err := tdc5.GetResourceQuota(tenantUid)
	if err != nil {
		stdlog.Errorf("Get %s's resource quota err: %+v", tenantUid, err)
		return nil, err
	}
	resourceQuota, err := toKubernetesResourceQuota(tenantUid, &quota.Hard)
	if err != nil {
		stdlog.Errorf("Transform To k8s resource quota err: %+v", err)
		return nil, err
	}

	initHippoHandlerChain := handler.NewTdc5HippoInitHandlerChain(tenantUid, tdc5.kclient)
	params := handler.HandlerParams{
		Namespace:     tenantUid,
		ResourceQuota: resourceQuota,
	}
	go initHippoHandlerChain.Handle(context.Background(), tenantUid, params)

	waitTime := 0
	for waitTime < 10 {
		installed, err := tdc5.tdc5client.IsHippoInstalled(context.Background(), tenantUid)
		if err != nil {
			return nil, err
		} else if installed {
			break
		} else {
			waitTime = waitTime + 1
			time.Sleep(1 * time.Second)
		}
	}
	return tdc5.GetHippo(tenantUid)
}

func (tdc5 *TDC5TenantService) GetHippoUsedStorage(tenantUid string) (string, error) {
	usedstorage, err := httpclient.GetHippoUsedStorage(tenantUid)
	if err != nil {
		stdlog.Errorf("Get hippo used storage failed, error: %s.", err.Error())
	}
	return fmt.Sprintf("%dGi", usedstorage), err
}

func (tdc5 *TDC5TenantService) GetResourceQuota(tenantUid string) (*models.TenantResourceQuota, error) {
	quota, err := getResourceQuota(tenantUid)
	if err != nil {
		return nil, err
	}

	hippoUsedStorage, err := tdc5.GetHippoUsedStorage(tenantUid)
	if err != nil {
		stdlog.Errorf("Get hippo used storage failed, error: %s.", err.Error())
	}
	quota.Used.Knowl = hippoUsedStorage
	quota.Used.KnowledgeBaseStorage = hippoUsedStorage

	return quota, nil
}

func (tdc5 *TDC5TenantService) GetDefaultResourceQuota() *models.TenantResourceQuota {
	return getDefaultResourceQuota()
}

func (tdc5 *TDC5TenantService) UpdateResourceQuota(ctx context.Context, ns string, quota *models.ResourceQuotaSpec) (*models.TenantResourceQuota, error) {
	resourceQuota, err := toKubernetesResourceQuota(ns, quota)
	if err != nil {
		return nil, err
	}
	annotations := make(map[string]string, 0)
	annotations[customtypes.TenantUidKey] = ns
	annotations[customtypes.NewSizeKey] = quota.FileStorage

	resourceQuota.Annotations = annotations

	params := handler.HandlerParams{
		Namespace:     ns,
		ResourceQuota: resourceQuota,
	}
	updateHandlerChain := handler.NewTdc5UpdateHandlerChain(ns, tdc5.kclient)
	err = updateHandlerChain.Handle(ctx, ns, params)
	if err != nil {
		return nil, err
	}

	return tdc5.GetResourceQuota(ns)
}

func (tdc5 *TDC5TenantService) ValidateTenantName(ctx context.Context, name string) error {
	tenants, err := tdc5.ListTenants(ctx)
	if err != nil {
		return err
	}
	for _, tenant := range tenants {
		if tenant.TenantName == name {
			return stderr.Errorf("tenant name %s exists", name)
		}
	}
	return nil
}

func (tdc5 *TDC5TenantService) ValidateTenantUid(ctx context.Context, uid string) error {
	newNsName, valid := util.GenNewNamespace(uid, true)
	if !valid || len(newNsName) > 20 {
		return stderr.Errorf(`Invalid tenant id %s, 1. can only contain lowercase alphanumeric characters (a-z, 0-9) and hyphens (-);
		2. must start and end with an alphanumeric character; 3. must be between 1 and 20 characters.`, newNsName)
	}

	tenants, err := tdc5.ListTenants(ctx)
	if err != nil {
		return err
	}
	for _, tenant := range tenants {
		if tenant.TenantName == newNsName {
			return stderr.Errorf("tenant uid %s exsits", newNsName)
		}
	}
	return nil
}

func (tdc5 *TDC5TenantService) CreateTenantIngress(uid string) error {
	hc := handler.NewIngressInitHandlerChain(uid, tdc5.kclient)
	return hc.Handle(context.TODO(), uid, handler.HandlerParams{
		Namespace: uid,
	})
}

func (tdc5 *TDC5TenantService) NxgToResourceQuota(nxgIDs []string) (*models.TenantResourceQuota, error) {
	return nxgToResourceQuota(nxgIDs)
}
