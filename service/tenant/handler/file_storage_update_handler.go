package handler

import (
	"context"
	"fmt"

	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	consts "transwarp.io/applied-ai/central-auth-service/service/tenant/consts"
	kubernetes "transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
	customtypes "transwarp.io/applied-ai/central-auth-service/service/tenant/types"
	"transwarp.io/applied-ai/central-auth-service/service/tenant/util"
)

var FileStorageUpdateHandlerName = "file_storage_update_handler"

type FileStorageUpdateHandler struct {
	name    string
	pre     IHandler
	next    IHandler
	kclient *kubernetes.KClientset
}

func init() {
	h := FileStorageUpdateHandler{
		name: FileStorageUpdateHandlerName,
	}
	RegisterHandler(FileStorageUpdateHandlerName, &h)
}

func NewFileStorageUpdateHandler(next IHandler, kclient *kubernetes.KClientset) IHandler {
	h := FileStorageUpdateHandler{
		name:    FileStorageUpdateHandlerName,
		next:    next,
		kclient: kclient,
	}
	return &h
}
func (h *FileStorageUpdateHandler) handle(ctx context.Context, namespace string, params HandlerParams) error {
	stdlog.Infof("Start exec %s's handler %s.", namespace, FileStorageUpdateHandlerName)
	if err := h.doHandle(ctx, namespace, params); err != nil {
		stdlog.Errorf("Update file storage failed: %v", err)
	}

	if h.next != nil {
		return h.next.handle(ctx, namespace, params)
	}
	return nil
}

func (h *FileStorageUpdateHandler) doHandle(ctx context.Context, namespace string, params HandlerParams) error {
	currnetNs, _ := util.GetCurrentNamespace()
	FileSystemStorageLabelSelector := fmt.Sprintf(consts.FileSystemStorageLabelSelectorFormat, currnetNs)

	var quantity resource.Quantity
	if v, ok := params.ResourceQuota.Spec.Hard[customtypes.ResourceFileStorage]; ok {
		quantity = v
	} else {
		stdlog.Errorf("Resource quota params not found.")
		return stderr.Error("Resource quota params not found.")
	}
	expectFileSystemStorage := quantity.Value() / (1 << 30)

	quotaName := util.GetResourceQuotaName(namespace)
	res, err := h.kclient.KubeClient.CoreV1().ResourceQuotas(namespace).Get(context.Background(), quotaName, metav1.GetOptions{})
	if err != nil {
		stdlog.Errorf("Get quota %s from ns %s failed, err: %+v", quotaName, namespace, err)
		return err
	}
	quantity = res.Spec.Hard[customtypes.ResourceFileStorage]
	currentFileStorageStorage := quantity.Value() / (1 << 30)

	stdlog.Infof("Current tenant %s file system storage: %dGi", namespace, currentFileStorageStorage)
	stdlog.Infof("Expect tenant %s file system storage: %dGi", namespace, expectFileSystemStorage)

	if currentFileStorageStorage < expectFileSystemStorage {
		// TODO maybe pathc pv first
		err = h.kclient.PatchPvcCapacityWithLableSelector(namespace, FileSystemStorageLabelSelector, expectFileSystemStorage-currentFileStorageStorage)
		if err != nil {
			stdlog.Errorf("Patch %s pvc with label %s failed: %+v", namespace, FileSystemStorageLabelSelector, err)
			//  patch failed set file storage to origin value
			params.ResourceQuota.Spec.Hard[customtypes.ResourceFileStorage] = res.Spec.Hard[customtypes.ResourceFileStorage]
			return err
		} else {
			h.kclient.PatchResourceQuota(namespace, quotaName, string(customtypes.ResourceFileStorage), util.GiToGi(expectFileSystemStorage), ctx.Value("username").(string))
			// 使用 expense 监听 k8s quota 代替
			// call expense api
			// username := ctx.Value("username").(string)
			// record := &client.ExpenseDiskRecord{
			// 	Applicant: username,
			// 	DiskSpace: expectFileSystemStorage - currentFileStorageStorage,
			// 	RuleType:  client.ExpenseRuleTypeSampleDisk,
			// 	TenantUid: namespace,
			// }
			// expenseClient := client.NewExpenseClient("")
			// err := expenseClient.RecordDiskConsumption(ctx, record)
			// if err != nil {
			// 	stdlog.Errorf("Call expense api file storage err: %+v", err)
			// 	return err
			// }
		}
	}

	return nil
}

func (h *FileStorageUpdateHandler) rollback(ctx context.Context, namespace string) {
	if h.pre != nil {
		h.pre.rollback(ctx, namespace)
	}
}
