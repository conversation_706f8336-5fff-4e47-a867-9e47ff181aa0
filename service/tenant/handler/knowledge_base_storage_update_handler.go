package handler

import (
	"context"
	"fmt"

	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/conf"
	consts "transwarp.io/applied-ai/central-auth-service/service/tenant/consts"
	kubernetes "transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
	customtypes "transwarp.io/applied-ai/central-auth-service/service/tenant/types"
	util "transwarp.io/applied-ai/central-auth-service/service/tenant/util"
)

var KnowledgeBaseStorageUpdateHandlerName = "knowledge_base_storage_update_handler"

type KnowledgeBaseStorageUpdateHandler struct {
	name    string
	pre     IHandler
	next    IHandler
	kclient *kubernetes.KClientset
}

func init() {
	h := KnowledgeBaseStorageUpdateHandler{
		name: KnowledgeBaseStorageUpdateHandlerName,
	}
	RegisterHandler(KnowledgeBaseStorageUpdateHandlerName, &h)
}

func NewKnowledgeBaseStorageUpdateHandler(next IHandler, kclient *kubernetes.KClientset) IHandler {
	h := KnowledgeBaseStorageUpdateHandler{
		name:    KnowledgeBaseStorageUpdateHandlerName,
		next:    next,
		kclient: kclient,
	}
	return &h
}
func (h *KnowledgeBaseStorageUpdateHandler) handle(ctx context.Context, namespace string, params HandlerParams) error {
	stdlog.Infof("Start exec %s's handler %s.", namespace, KnowledgeBaseStorageUpdateHandlerName)
	if err := h.doHandle(ctx, namespace, params); err != nil {
		stdlog.Errorf("Update knowledge base storage failed: %v", err)
	}

	if h.next != nil {
		return h.next.handle(ctx, namespace, params)
	}
	return nil
}

func (h *KnowledgeBaseStorageUpdateHandler) doHandle(ctx context.Context, namespace string, params HandlerParams) error {
	currnetNs, _ := util.GetCurrentNamespace()
	K8sHippoPvcLabelSelector := fmt.Sprintf(consts.K8sHippoPvcLabelSelectorFormat, currnetNs)

	var quantity resource.Quantity
	if v, ok := params.ResourceQuota.Spec.Hard[customtypes.ResourceKnowledgeBaseStorage]; ok {
		quantity = v
	} else {
		stdlog.Errorf("Resource quota params not found.")
		return stderr.Error("Resource quota params not found.")
	}
	expectKnowledgeBaseStorage := quantity.Value() / (1 << 30)

	quotaName := util.GetResourceQuotaName(namespace)
	res, err := h.kclient.KubeClient.CoreV1().ResourceQuotas(namespace).Get(context.Background(), quotaName, metav1.GetOptions{})
	if err != nil {
		stdlog.Errorf("Get quota %s from ns %s failed, err: %+v", quotaName, namespace, err)
		return err
	}
	quantity = res.Spec.Hard[customtypes.ResourceKnowledgeBaseStorage]
	currentKnowledgeBaseStorage := quantity.Value() / (1 << 30)

	stdlog.Infof("Current tenant %s knowledge base storage: %dGi", namespace, currentKnowledgeBaseStorage)
	stdlog.Infof("Expect tenant %s knowledge base storage: %dGi", namespace, expectKnowledgeBaseStorage)

	deltaSize := expectKnowledgeBaseStorage - currentKnowledgeBaseStorage
	if deltaSize > 0 {
		var labelSelector = K8sHippoPvcLabelSelector
		if conf.C.Tenant.Strategy == "tdc" {
			labelSelector = consts.TdcHippoPvcLabelSelector
		}

		err = h.kclient.PatchPvcCapacityWithLableSelector(namespace, labelSelector, deltaSize)
		if err != nil {
			stdlog.Errorf("Patch %s pvc with label %s failed: %+v", namespace, labelSelector, err)
			//  patch failed set file storage to origin value
			params.ResourceQuota.Spec.Hard[customtypes.ResourceKnowledgeBaseStorage] = res.Spec.Hard[customtypes.ResourceKnowledgeBaseStorage]
			return err
		} else {
			h.kclient.PatchResourceQuota(namespace, quotaName, string(customtypes.ResourceKnowledgeBaseStorage), util.GiToGi(expectKnowledgeBaseStorage), ctx.Value("username").(string))
			// 使用 expense 监听 k8s quota 代替
			// call expense api
			// username := ctx.Value("username").(string)
			// record := &client.ExpenseDiskRecord{
			// 	Applicant: username,
			// 	DiskSpace: expectKnowledgeBaseStorage - currentKnowledgeBaseStorage,
			// 	RuleType:  client.ExpenseRuleTypeSampleKnowledge,
			// 	TenantUid: namespace,
			// }
			// expenseClient := client.NewExpenseClient("")
			// err = expenseClient.RecordDiskConsumption(ctx, record)
			// if err != nil {
			// 	stdlog.Errorf("Call expense api knowledge base storage err: %+v", err)
			// 	return err
			// }
		}
	}
	return nil
}

func (h *KnowledgeBaseStorageUpdateHandler) rollback(ctx context.Context, namespace string) {
	if h.pre != nil {
		h.pre.rollback(ctx, namespace)
	}
}
