package resourcequota

import (
	"context"
	"fmt"
	"sync"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/models"
	k8s "transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
	"transwarp.io/applied-ai/central-auth-service/service/tenant/util"
)

type Manager struct {
	quotaService QuotaService
	ctx          context.Context
	cancel       context.CancelFunc
	mu           sync.RWMutex
}

var (
	manager *Manager
	once    sync.Once
)

func MustGetManager() *Manager {
	var initErr error
	once.Do(func() {
		m, initErr := initManager()
		if initErr == nil {
			manager = m
		}
	})
	if initErr != nil {
		panic(initErr)
	}
	return manager
}

func initManager() (*Manager, error) {
	quotaRepo := NewQuotaRepo()
	configRepo := NewConfigMapRepo()

	calculator := NewQuotaCalculator()
	validator := NewQuotaValidator()
	renderer := NewHelmValuesRenderer()

	nxpuClient := NewXpuClient()

	quotaService := NewQuotaService(
		quotaRepo,
		configRepo,
		calculator,
		validator,
		renderer,
		nxpuClient,
	)

	ctx, cancel := context.WithCancel(context.Background())

	manager := &Manager{
		quotaService: quotaService,
		ctx:          ctx,
		cancel:       cancel,
	}

	if err := manager.startResourceQuotaManagerLeaderElection(ctx); err != nil {
		return nil, err
	}

	if err := manager.startWatchers(); err != nil {
		cancel()
		return nil, stderr.Errorf("failed to start watchers: %v", err)
	}

	return manager, nil
}

func (m *Manager) startResourceQuotaManagerLeaderElection(ctx context.Context) error {
	onStartedLeading := func(ctx context.Context) {
	}
	onStoppedLeading := func() {
	}
	onNewLeader := func(identity string) {
	}

	leConfig := util.NewDefaultLeaderElectionConfig(
		util.DefaultLeaderElectionID,
		util.GetCurrentNamespaceOrDefault("llmops"),
		util.GetCurrentPodNameOrDefault("cas"),
		k8s.MustGetKClientset().KubeClient,
		onStartedLeading,
		onStoppedLeading,
		onNewLeader,
		util.DefaultLeaderElectionLeaseDuration,
		util.DefaultLeaderElectionRenewDeadline,
		util.DefaultLeaderElectionRetryPeriod,
	)

	if err := util.StartLeaderElection(ctx, leConfig); err != nil {
		return fmt.Errorf("failed to start task handler leader election: %w", err)
	}

	return nil
}

func (m *Manager) GetQuota(namespace string) (*models.TenantResourceQuota, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	return m.quotaService.GetQuota(m.ctx, namespace)
}

func (m *Manager) CreateOrUpdateQuota(namespace string, spec *models.ResourceQuotaSpec) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	return m.quotaService.CreateOrUpdateQuota(m.ctx, namespace, spec)
}

func (m *Manager) GetDefaultQuota() *models.TenantResourceQuota {
	m.mu.RLock()
	defer m.mu.RUnlock()

	return m.quotaService.GetDefaultQuota()
}

func (m *Manager) GetClusterLimits() (*models.ResourceQuotaSpec, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	return m.quotaService.GetClusterLimits()
}

func (m *Manager) ValidateQuota(spec *models.ResourceQuotaSpec) error {
	return m.quotaService.ValidateQuota(spec)
}

func (m *Manager) CalculateQuotaFromXPUGroups(groupIDs []string) (*models.ResourceQuotaSpec, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	return m.quotaService.CalculateQuotaFromXPUGroups(groupIDs)
}

func (m *Manager) startWatchers() error {
	if err := m.quotaService.StartConfigWatcher(m.ctx); err != nil {
		return stderr.Errorf("failed to start config watcher: %v", err)
	}

	if err := m.quotaService.StartXPUWatcher(m.ctx); err != nil {
		stdlog.Errorf("Failed to start XPU watcher: %v", err)
	}

	stdlog.Infof("Resource quota manager watchers started successfully")
	return nil
}

func (m *Manager) Cleanup() {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.cancel != nil {
		m.cancel()
	}

	stdlog.Infof("Resource quota manager cleaned up")
}

func (m *Manager) Restart() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.cancel != nil {
		m.cancel()
	}

	newManager, err := initManager()
	if err != nil {
		return stderr.Errorf("failed to restart manager: %v", err)
	}

	m.quotaService = newManager.quotaService
	m.ctx = newManager.ctx
	m.cancel = newManager.cancel

	stdlog.Infof("Resource quota manager restarted successfully")
	return nil
}

func (m *Manager) GetStatus() map[string]interface{} {
	m.mu.RLock()
	defer m.mu.RUnlock()

	status := map[string]interface{}{
		"running": m.ctx.Err() == nil,
	}

	if m.ctx.Err() != nil {
		status["error"] = m.ctx.Err().Error()
	}

	return status
}

func (m *Manager) CreateOrUpgradeResourceQuota(namespace string, spec *models.ResourceQuotaSpec) error {
	return m.CreateOrUpdateQuota(namespace, spec)
}

func (m *Manager) NxgToResourceQuotaSpec(groupIDs []string) (*models.ResourceQuotaSpec, error) {
	return m.CalculateQuotaFromXPUGroups(groupIDs)
}
