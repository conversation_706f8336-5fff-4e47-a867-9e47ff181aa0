package resourcequota

const (
	ResourceQuotaConfigKey = "resourcequota.yaml"
	DefaultQuotaName       = "default"
	MaxNamespaceLength     = 63

	Zero   = "0"
	ZeroGi = "0Gi"
	ZeroMi = "0Mi"

	DefaultLimitsCPU        = "100000"
	DefaultLimitsMemory     = "100000Gi"
	DefaultPods             = "100000"
	DefaultRequestsCPU      = "100000"
	DefaultRequestsMemory   = "100000Gi"
	DefaultRequestsStorage  = "10000000Gi"
	DefaultBandwidth        = "1000Gi"
	DefaultEgressBandwidth  = "1000Gi"
	DefaultIngressBandwidth = "1000Gi"
	DefaultGPU              = "100000"
	DefaultGPUMemory        = "100000Gi"
	DefaultKnowledgeStorage = "100000Gi"
	DefaultFileStorage      = "100000Gi"

	ConfigMapLabelManagedBy = "app.kubernetes.io/managed-by"
	ConfigMapLabelComponent = "app.kubernetes.io/component"
	ConfigMapLabelInstance  = "app.kubernetes.io/instance"
	ConfigMapManagedByValue = "central-auth-service"
	ConfigMapComponentValue = "resource-quota"

	QueueTypeDefault = "default"
	QueueTypeTask    = "task"
	QueueTypeInfer   = "infer"

	DefaultQueueWeight = 1.0
	MaxWeight          = 1.0
	MinWeight          = 0.0
)

const (
	UnitCPU    = "1"
	UnitMemory = "1Gi"
	UnitGPU    = "1"
	UnitBytes  = "1"
)

const (
	ErrMsgInvalidNamespace     = "invalid namespace name"
	ErrMsgInvalidQuotaSpec     = "invalid quota specification"
	ErrMsgInvalidQuotaItem     = "invalid quota item"
	ErrMsgQuotaExceedsLimit    = "quota exceeds cluster limits"
	ErrMsgTemplateNotFound     = "template not found"
	ErrMsgTemplateRenderFailed = "template render failed"
	ErrMsgHelmOperationFailed  = "helm operation failed"
	ErrMsgXPUGroupNotFound     = "xpu group not found"
	ErrMsgConfigNotFound       = "configuration not found"
)
