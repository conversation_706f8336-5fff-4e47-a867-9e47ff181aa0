package resourcequota

import (
	"fmt"
	"os"
	"time"

	"gopkg.in/yaml.v3"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/models"
)

type LlmopsQueueHelmValues struct {
	Resourcequota LlmopsQueueResourceQuota `json:"resourcequota" yaml:"resourcequota"`
}

type LlmopsQueueResourceQuota struct {
	Cohort models.ResourceQuotaSpec `json:"cohort" yaml:"cohort"`
}

type HelmValuesRenderer struct {
	templateDir string
}

func NewHelmValuesRenderer() *HelmValuesRenderer {
	return &HelmValuesRenderer{
		templateDir: "/tmp",
	}
}

func (r *HelmValuesRenderer) RenderValues(namespace string, spec *models.ResourceQuotaSpec) (string, error) {
	values := LlmopsQueueHelmValues{
		Resourcequota: LlmopsQueueResourceQuota{
			Cohort: *spec,
		},
	}

	valuesPath := fmt.Sprintf("%s/%s-quota-values-%s.yaml", r.templateDir, namespace,
		func() string {
			now := time.Now()
			year, month, day := now.Date()

			return fmt.Sprintf("%d-%02d-%02d", year, month, day)
		}())

	file, err := os.Create(valuesPath)
	if err != nil {
		stdlog.Errorf("Failed to create values file for namespace %s: %v", namespace, err)
		return "", err
	}
	defer file.Close()

	fileEncoder := yaml.NewEncoder(file)
	fileEncoder.SetIndent(2)
	if err = fileEncoder.Encode(values); err != nil {
		stdlog.Errorf("Failed to encode values to YAML for namespace %s: %v", namespace, err)
		return "", err
	}

	if err = fileEncoder.Close(); err != nil {
		stdlog.Errorf("Failed to close encoder for namespace %s: %v", namespace, err)
		return "", err
	}

	stdlog.Infof("Successfully rendered values file for namespace %s at %s", namespace, valuesPath)
	return valuesPath, nil
}
