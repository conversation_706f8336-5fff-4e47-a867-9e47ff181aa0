package resourcequota

import (
	"context"
	"fmt"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/tools/cache"

	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	k8s "transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
	"transwarp.io/applied-ai/central-auth-service/service/tenant/util"
)

type ConfigChangeEvent struct {
	Namespace string
	EventType ConfigEventType
}

type ConfigEventType string

const (
	ConfigEventTypeCreated ConfigEventType = "created"
	ConfigEventTypeUpdated ConfigEventType = "updated"
	ConfigEventTypeDeleted ConfigEventType = "deleted"
)

type ConfigRepo interface {
	GetConfig(ctx context.Context, namespace string) (map[string]string, error)
	SaveConfig(ctx context.Context, namespace string, config map[string]string) error
	DeleteConfig(ctx context.Context, namespace string) error
	WatchConfig(ctx context.Context) (<-chan ConfigChangeEvent, error)
}

type ConfigMapRepo struct {
	client *k8s.KClientset
	stopCh chan struct{}
}

func NewConfigMapRepo() ConfigRepo {
	return &ConfigMapRepo{
		client: k8s.MustGetKClientset(),
		stopCh: make(chan struct{}),
	}
}

func (r *ConfigMapRepo) generateConfigMapName(namespace string) string {
	return fmt.Sprintf("%s-quota", namespace)
}

func (r *ConfigMapRepo) GetConfig(ctx context.Context, namespace string) (map[string]string, error) {
	configMapName := r.generateConfigMapName(namespace)

	configMap, err := r.client.K8sInformerClient.ConfigMapLister.ConfigMaps(namespace).Get(configMapName)
	if err != nil {
		return nil, stderr.Errorf("failed to get config map: %v", err)
	}

	return configMap.Data, nil
}

func (r *ConfigMapRepo) SaveConfig(ctx context.Context, namespace string, config map[string]string) error {
	configMapName := r.generateConfigMapName(namespace)

	configMap := &corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      configMapName,
			Namespace: namespace,
			Labels:    r.getLabels(),
		},
		Data: config,
	}

	existing, err := r.client.K8sInformerClient.ConfigMapLister.ConfigMaps(namespace).Get(configMapName)
	if err != nil {
		_, err = r.client.KubeClient.CoreV1().ConfigMaps(namespace).Create(ctx, configMap, metav1.CreateOptions{})
		if err != nil {
			return stderr.Errorf("failed to create config map: %v", err)
		}
		stdlog.Infof("Created config map for namespace %s", namespace)
	} else {
		existing.Data = config
		existing.Labels = configMap.Labels

		_, err = r.client.KubeClient.CoreV1().ConfigMaps(namespace).Update(ctx, existing, metav1.UpdateOptions{})
		if err != nil {
			return stderr.Errorf("failed to update config map: %v", err)
		}
		stdlog.Infof("Updated config map for namespace %s", namespace)
	}

	return nil
}

func (r *ConfigMapRepo) DeleteConfig(ctx context.Context, namespace string) error {
	configMapName := r.generateConfigMapName(namespace)

	err := r.client.KubeClient.CoreV1().ConfigMaps(namespace).Delete(ctx, configMapName, metav1.DeleteOptions{})
	if err != nil {
		return stderr.Errorf("failed to delete config map: %v", err)
	}

	stdlog.Infof("Deleted config map for namespace %s", namespace)
	return nil
}

func (r *ConfigMapRepo) getLabels() map[string]string {
	return util.WithManagedByLabels(map[string]string{
		labelKeyResourceQuotaSpec: labelValueResourceQuotaSpec,
	})
}

func (r *ConfigMapRepo) WatchConfig(ctx context.Context) (<-chan ConfigChangeEvent, error) {
	eventChan := make(chan ConfigChangeEvent, DefaultEventChanSize)

	informer := r.client.K8sInformerClient.ConfigMapInformer
	// Add event handler with label filtering
	informer.AddEventHandler(cache.FilteringResourceEventHandler{
		FilterFunc: func(obj interface{}) bool {
			cm, ok := obj.(*corev1.ConfigMap)
			if !ok {
				return false
			}
			return util.ContainLabels(cm.Labels, r.getLabels())
		},
		Handler: cache.ResourceEventHandlerFuncs{
			AddFunc: func(obj interface{}) {
				cm := obj.(*corev1.ConfigMap)
				if util.IsLeader() {
					eventChan <- ConfigChangeEvent{
						Namespace: cm.Namespace,
						EventType: ConfigEventTypeCreated,
					}
				}
			},
			UpdateFunc: func(oldObj, newObj interface{}) {
				oldCM := oldObj.(*corev1.ConfigMap)
				newCM := newObj.(*corev1.ConfigMap)
				if oldCM.ResourceVersion == newCM.ResourceVersion {
					return
				}
				if util.IsLeader() {
					eventChan <- ConfigChangeEvent{
						Namespace: newCM.Namespace,
						EventType: ConfigEventTypeUpdated,
					}
				}
			},
		},
	})

	if !cache.WaitForCacheSync(r.stopCh, informer.HasSynced) {
		stdlog.Errorf("Failed to sync resource quota ConfigMap informer.")
	}

	return eventChan, nil
}
