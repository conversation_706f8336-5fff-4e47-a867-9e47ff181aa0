package resourcequota

import (
	"fmt"
	"math"
	"regexp"
	"strconv"

	"k8s.io/apimachinery/pkg/api/resource"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/models"
	k8s "transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
	"transwarp.io/applied-ai/central-auth-service/service/tenant/util"
)

type QuotaCalculator struct {
	client *k8s.KClientset
}

func NewQuotaCalculator() *QuotaCalculator {
	return &QuotaCalculator{
		client: k8s.MustGetKClientset(),
	}
}

func (c *QuotaCalculator) RecalculateQuota(spec *models.ResourceQuotaSpec) error {
	if spec.Queue == nil {
		return nil
	}

	// recal quota if enable weight mode
	if err := c.recalculateQuotaIfEnableWeightMode(spec); err != nil {
		return err
	}

	// convert xpu mem quota to count
	c.convertXPUMemQuotaToCount(spec.XPU)
	c.convertXPUMemQuotaToCount(spec.Queue.Task.XPU)
	c.convertXPUMemQuotaToCount(spec.Queue.Infer.XPU)
	c.convertXPUMemQuotaToCount(spec.Queue.Default.XPU)

	// apply borrowing limit for queue
	c.applyQueueQuotaBorrowingLimit(&spec.Queue.Default)
	c.applyQueueQuotaBorrowingLimit(&spec.Queue.Infer)
	c.applyQueueQuotaBorrowingLimit(&spec.Queue.Task)

	return nil
}

func (c *QuotaCalculator) CalculateXPUQuota(xpuGroups []string) (map[string][]*models.ResourceQuotaItem, error) {
	return make(map[string][]*models.ResourceQuotaItem), nil
}

func (c *QuotaCalculator) CalculateClusterTotals() (cpu, memory string, err error) {
	return c.client.CalClusterTotalCPUAndMemory()
}

func (c *QuotaCalculator) convertXPUMemQuotaToCount(ac map[string][]*models.ResourceQuotaItem) {
	for _, items := range ac {
		for _, item := range items {
			if item.QuotaItemType == models.QuotaItemTypeXPUMem {
				if item.NominalQuota == "" || item.NominalQuota == Zero || item.Unit == "" {
					continue
				}
				totalMem := resource.MustParse(item.NominalQuota)
				unit := resource.MustParse(item.Unit)
				count := totalMem.Value() / unit.Value()
				item.NominalQuota = strconv.FormatInt(count, 10)
			}
		}
	}
}

func (c *QuotaCalculator) recalculateQuotaIfEnableWeightMode(spec *models.ResourceQuotaSpec) error {
	if spec.Queue == nil || !spec.WeightMode {
		return nil
	}

	// TODO default queue weight is 1.0, user all tenant resource
	w := 1.0
	spec.Queue.Default.CPU.NominalQuota, _ = c.calculateByWeight(spec.CPU.NominalQuota, w)
	spec.Queue.Default.Memory.NominalQuota, _ = c.calculateByWeight(spec.Memory.NominalQuota, w)
	for _, v := range spec.Queue.Default.XPU {
		for _, u := range v {
			u.NominalQuota, _ = util.CalculateByWeight(u.NominalQuota, w)
		}
	}

	// infer
	w = spec.CPU.Weight / 100.0 * spec.Queue.Infer.CPU.Weight / 100.0
	spec.Queue.Infer.CPU.NominalQuota, _ = c.calculateByWeight(spec.CPU.NominalQuota, w)
	w = spec.Memory.Weight / 100.0 * spec.Queue.Infer.Memory.Weight / 100.0
	spec.Queue.Infer.Memory.NominalQuota, _ = c.calculateByWeight(spec.Memory.NominalQuota, w)
	for u, v := range spec.Queue.Infer.XPU {
		for _, vv := range v {
			for x, y := range spec.XPU {
				if u == x {
					for _, yy := range y {
						if vv.Name == yy.Name {
							w = yy.Weight / 100.0 * vv.Weight / 100.0
							vv.NominalQuota, _ = c.calculateByWeight(vv.NominalQuota, w)
							break
						}
					}
					break
				}
			}
		}
	}
	// task
	w = spec.CPU.Weight / 100.0 * spec.Queue.Task.CPU.Weight / 100.0
	spec.Queue.Task.CPU.NominalQuota, _ = c.calculateByWeight(spec.CPU.NominalQuota, w)
	w = spec.Memory.Weight / 100.0 * spec.Queue.Task.Memory.Weight / 100.0
	spec.Queue.Task.Memory.NominalQuota, _ = c.calculateByWeight(spec.Memory.NominalQuota, w)
	for u, v := range spec.Queue.Task.XPU {
		for _, vv := range v {
			for x, y := range spec.XPU {
				if u == x {
					for _, yy := range y {
						if vv.Name == yy.Name {
							w = yy.Weight / 100.0 * vv.Weight / 100.0
							vv.NominalQuota, _ = c.calculateByWeight(vv.NominalQuota, w)
							break
						}
					}
					break
				}
			}
		}
	}

	w = spec.CPU.Weight / 100.0
	spec.CPU.NominalQuota, _ = c.calculateByWeight(spec.CPU.NominalQuota, w)
	w = spec.Memory.Weight / 100.0
	spec.Memory.NominalQuota, _ = c.calculateByWeight(spec.Memory.NominalQuota, w)
	for _, v := range spec.XPU {
		for _, u := range v {
			w := u.Weight / 100.0
			u.NominalQuota, _ = c.calculateByWeight(u.NominalQuota, w)
		}
	}

	return nil
}

func (c *QuotaCalculator) applyQueueQuotaBorrowingLimit(spec *models.QueueResourceQuotaSpec) {
	spec.CPU.BorrowingLimit = spec.CPU.NominalQuota
	spec.Memory.BorrowingLimit = spec.Memory.NominalQuota

	spec.CPU.NominalQuota = Zero
	spec.Memory.NominalQuota = ZeroGi

	for _, items := range spec.XPU {
		for _, item := range items {
			item.BorrowingLimit = item.NominalQuota
			item.NominalQuota = Zero
		}
	}
}

func (c *QuotaCalculator) calculateByWeight(quotaStr string, weight float64) (string, error) {
	if weight > MaxWeight {
		weight = MaxWeight
	}
	if weight < MinWeight {
		weight = MinWeight
	}

	re := regexp.MustCompile(`^(\d+)(.*)$`)
	matches := re.FindStringSubmatch(quotaStr)

	if len(matches) > 1 {
		v, err := strconv.ParseInt(matches[1], 10, 64)
		if err != nil {
			return quotaStr, stderr.Errorf("failed to parse quantity: %v", err)
		}

		if len(matches) > 2 && matches[2] != "" {
			res := int64(math.Ceil(float64(v) * weight))
			return fmt.Sprintf("%d%s", res, matches[2]), nil
		}

		res := int64(math.Ceil(float64(v) * weight))
		return strconv.FormatInt(res, 10), nil
	}

	stdlog.Errorf("Invalid quantity quota str: %s", quotaStr)
	return quotaStr, stderr.Errorf("invalid quantity quota str: %s", quotaStr)
}

func (c *QuotaCalculator) ValidateResourceConstraints(spec *models.ResourceQuotaSpec) error {
	if spec.CPU.NominalQuota != "" && spec.Memory.NominalQuota != "" {
		cpuQuantity, err := resource.ParseQuantity(spec.CPU.NominalQuota)
		if err != nil {
			return stderr.Errorf("invalid CPU quantity: %v", err)
		}

		memoryQuantity, err := resource.ParseQuantity(spec.Memory.NominalQuota)
		if err != nil {
			return stderr.Errorf("invalid Memory quantity: %v", err)
		}

		cpuCores := cpuQuantity.Value()
		memoryGB := memoryQuantity.Value() / (1 << 30)

		if memoryGB < cpuCores {
			stdlog.Warnf("Memory to CPU ratio may be too low: %dGB memory for %d CPU cores", memoryGB, cpuCores)
		}
	}

	return nil
}
