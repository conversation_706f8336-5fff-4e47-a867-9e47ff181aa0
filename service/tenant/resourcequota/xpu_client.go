package resourcequota

import (
	"context"
	"sync"
	"time"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/tools/cache"
	xpu "transwarp.io/aip/llmops-common/pkg/crd"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/models"
	k8s "transwarp.io/applied-ai/central-auth-service/service/tenant/kubernetes"
	"transwarp.io/applied-ai/central-auth-service/service/tenant/util"
	knm_v1 "transwarp.io/applied-ai/kube-nodexpu-manager/apis/resources/v1alpha1"
)

// xpu.kubernetes.io/resource-type-key: nvidia | hygon | iluvatar | cambricon | mlu | metax | mthreads
// xpu.kubernetes.io/cores-key: "cores.llmops.transwarp.io/iluvatar"
// xpu.kubernetes.io/mem-key: "mem.llmops.transwarp.io/iluvatar"
// xpu.kubernetes.io/mem-unit-key: "mem-unit.llmops.transwarp.io/iluvatars"
const (
	AnnotationXPUResourceTypeKey = "xpu.kubernetes.io/resource-type-key"

	AnnotationXPUResourceCoresKey   = "xpu.kubernetes.io/cores-key"
	AnnotationXPUResourceKey        = "xpu.kubernetes.io/mem-key"
	AnnotationXPUResourceMemUnitKey = "xpu.kubernetes.io/mem-unit-key"
)

type XPUGroupChangeEvent struct {
	GroupID   string
	EventType XPUGroupEventType
}

type XPUGroupEventType string

const (
	XPUGroupEventTypeCreated XPUGroupEventType = "created"
	XPUGroupEventTypeUpdated XPUGroupEventType = "updated"
	XPUGroupEventTypeDeleted XPUGroupEventType = "deleted"
)

type NXPUClient struct {
	kclient *k8s.KClientset

	stopCh chan struct{}

	mu            sync.RWMutex
	avaliableXPUs map[string][]*models.ResourceQuotaItem
}

func NewXpuClient() *NXPUClient {
	kclient, err := k8s.NewKClientset()
	if err != nil {
		panic(err)
	}
	c := &NXPUClient{
		kclient:       kclient,
		stopCh:        make(chan struct{}),
		mu:            sync.RWMutex{},
		avaliableXPUs: make(map[string][]*models.ResourceQuotaItem),
	}
	go c.initAvaliableXPUs(10 * time.Minute)
	return c
}

func (c *NXPUClient) initAvaliableXPUs(interval time.Duration) {
	c.refreshAvaliableXPUs()
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for range ticker.C {
		c.refreshAvaliableXPUs()
	}
}

func (c *NXPUClient) refreshAvaliableXPUs() {
	avaliableXPUs, err := c.LimitXPUResources(context.Background())
	if err != nil {
		stdlog.Errorf("Failed to update XPUs resource cache: %v", err)
		return
	}

	// only keep type, quota set to zero
	for _, v := range avaliableXPUs {
		for _, item := range v {
			item.NominalQuota = util.ZeroWithUnit(item.NominalQuota)
			item.BorrowingLimit = ""
			item.LendingLimit = ""
		}
	}

	c.mu.Lock()
	defer c.mu.Unlock()
	c.avaliableXPUs = avaliableXPUs

	stdlog.Infof("XPU resource cache updated successfully.")
}

func (c *NXPUClient) WatchXPUGroups(ctx context.Context) (<-chan XPUGroupChangeEvent, error) {
	eventChan := make(chan XPUGroupChangeEvent, DefaultEventChanSize)

	informer := xpu.Rg.GroupInformer.Informer()

	informer.AddEventHandler(cache.FilteringResourceEventHandler{
		FilterFunc: func(obj interface{}) bool {
			nxg, ok := obj.(*knm_v1.NodeXpuGroup)
			if !ok {
				return false
			}
			return util.ContainLabels(nxg.Labels, util.GetResourceGroupManagedNamespaceLabels())
		},
		Handler: cache.ResourceEventHandlerFuncs{
			AddFunc: func(obj interface{}) {
				// create nxg not bind to ns, ignore
				// nxg := obj.(*knm_v1.NodeXpuGroup)
				// eventChan <- XPUGroupChangeEvent{
				// 	GroupID:   nxg.GetName(),
				// 	EventType: XPUGroupEventTypeCreated,
				// }
			},
			UpdateFunc: func(oldObj, newObj interface{}) {
				oldNxg := oldObj.(*knm_v1.NodeXpuGroup)
				newNxg := newObj.(*knm_v1.NodeXpuGroup)
				if oldNxg.ResourceVersion == newNxg.ResourceVersion {
					return
				}
				if util.IsLeader() {
					eventChan <- XPUGroupChangeEvent{
						GroupID:   newNxg.GetName(),
						EventType: XPUGroupEventTypeUpdated,
					}
				}
			},
			DeleteFunc: func(obj interface{}) {
				nxg, ok := obj.(*knm_v1.NodeXpuGroup)
				if !ok {
					return
				}
				if util.IsLeader() {
					eventChan <- XPUGroupChangeEvent{
						GroupID:   nxg.GetName(),
						EventType: XPUGroupEventTypeDeleted,
					}
				}
			},
		},
	})

	if !cache.WaitForCacheSync(c.stopCh, informer.HasSynced) {
		stdlog.Errorf("Failed to sync nx group informer.")
	}

	return eventChan, nil
}

func (c *NXPUClient) LimitXPUResources(ctx context.Context) (map[string][]*models.ResourceQuotaItem, error) {
	nxpus, err := xpu.Rg.ListXpus(ctx)
	if err != nil {
		return nil, err
	}

	return c.NXPUsToACResources(nxpus)
}
func (c *NXPUClient) NXPUGroupToResourceQuotaSpec(groupIDs []string) (*models.ResourceQuotaSpec, error) {
	spec := &models.ResourceQuotaSpec{
		// QuotaType:            models.QuotaTypeDynamic,
		XPU: make(map[string][]*models.ResourceQuotaItem),
	}

	groups, err := xpu.Rg.GroupLister.List(labels.Everything())
	if err != nil {
		return nil, err
	}

	nxpus := make([]*knm_v1.NodeXpu, 0)
	nodeNames := make(map[string]interface{}, 0)

	nxpuIDs := make([]string, 0)
	for _, group := range groups {
		if !util.Contains(groupIDs, group.GetName()) {
			continue
		}
		for _, node := range group.Spec.Nodes {
			xpuIDs := node.XpuIds
			// if group not include xpuIDs, only cpu and memory
			// if xpuIDs != nil && len(xpuIDs) > 0 {
			nodeNames[node.NodeName] = nil
			// }
			nxpuIDs = append(nxpuIDs, xpuIDs...)
		}
	}

	nxpuIDs = util.RemoveDuplicatesString(nxpuIDs)

	xpus, err := xpu.Rg.ListXpus(context.Background())
	if err != nil {
		return nil, err
	}

	for _, id := range nxpuIDs {
		for _, xpu := range xpus {
			if xpu.Spec.ID == id {
				nxpus = append(nxpus, xpu)
				break
			}
		}
	}

	ac, err := c.NXPUsToACResources(nxpus)
	if err != nil {
		return spec, nil
	}
	spec.XPU = ac

	// cpu and mem
	nns := make([]string, 0)
	for k, _ := range nodeNames {
		nns = append(nns, k)
	}
	totalCpu, totalMemory, err := c.kclient.CalNodesTotalCPUAndMemory(nns)
	if err != nil {
		return nil, err
	}
	spec.LimitsCpu = totalCpu
	spec.LimitsMemory = totalMemory
	spec.RequestsCpu = totalCpu
	spec.RequestsMemory = totalMemory
	spec.CPU = models.ResourceQuotaItem{
		Name:          corev1.ResourceCPU,
		NominalQuota:  totalCpu,
		QuotaItemType: models.QuotaItemTypeCPU,
		Unit:          "1",
	}
	spec.Memory = models.ResourceQuotaItem{
		Name:          corev1.ResourceMemory,
		NominalQuota:  totalMemory,
		QuotaItemType: models.QuotaItemTypeMem,
		Unit:          "1Gi",
	}

	models.ApplyDefaultQuotaForQueue(spec)

	return spec, nil
}

type ACResource struct {
	Name             string
	CoresResourceKey string
	MemResourceKey   string
	Cores            resource.Quantity
	Mem              resource.Quantity
	MemUnit          resource.Quantity
}

func (c *NXPUClient) NXPUsToACResources(nxpus []*knm_v1.NodeXpu) (map[string][]*models.ResourceQuotaItem, error) {

	acMap := make(map[string]*ACResource)

	for _, nxpu := range nxpus {
		annotations := nxpu.GetAnnotations()
		resourceType := annotations[AnnotationXPUResourceTypeKey]
		coresKey := annotations[AnnotationXPUResourceCoresKey]
		memKey := annotations[AnnotationXPUResourceKey]
		memUnitKey := annotations[AnnotationXPUResourceMemUnitKey]

		if resourceType == "" || coresKey == "" || memKey == "" || memUnitKey == "" {
			continue
		}

		if _, ok := acMap[resourceType]; !ok {
			acMap[resourceType] = &ACResource{
				Name: resourceType,
			}
		}
		coresVal := annotations[coresKey]
		if coresVal == "" {
			continue
		}
		acMap[resourceType].CoresResourceKey = coresKey
		acMap[resourceType].Cores.Add(resource.MustParse(coresVal))

		memVal := annotations[memKey]
		memUnitVal := annotations[memUnitKey]
		if memVal == "" || memUnitVal == "" {
			continue
		}
		acMap[resourceType].MemResourceKey = memKey
		acMap[resourceType].MemUnit = resource.MustParse(memUnitVal)

		totalMem := resource.MustParse(memUnitVal)
		count := resource.MustParse(memVal)
		totalMem.Mul(count.Value())
		acMap[resourceType].Mem.Add(totalMem)
	}
	ac := make(map[string][]*models.ResourceQuotaItem)
	for _, v := range acMap {
		ac[v.Name] = []*models.ResourceQuotaItem{
			{
				Name:          corev1.ResourceName(v.CoresResourceKey),
				NominalQuota:  v.Cores.String(),
				Unit:          "1",
				QuotaItemType: models.QuotaItemTypeXPUCores,
			},
			{
				Name:          corev1.ResourceName(v.MemResourceKey),
				NominalQuota:  util.ToGiStrCeil(v.Mem),
				Unit:          v.MemUnit.String(),
				QuotaItemType: models.QuotaItemTypeXPUMem,
			},
		}
	}

	c.addXPUWithZeroQuotaIfNotExist(&ac)
	return ac, nil
}

func (c *NXPUClient) addXPUWithZeroQuotaIfNotExist(ac *map[string][]*models.ResourceQuotaItem) {
	for k, v := range c.avaliableXPUs {
		if _, ok := (*ac)[k]; !ok {
			(*ac)[k] = v
		}
	}
}

func (c *NXPUClient) RecalculateNamespaceResourceQuotaEffectByNXG(groupID string) (map[string]models.ResourceQuotaSpec, error) {
	bindings, err := xpu.Rg.BindLister.List(labels.Everything())
	if err != nil {
		return nil, err
	}
	// find effected namespace
	effectedNS := make(map[string][]string, 0)
	for _, binding := range bindings {
		if binding.Spec.NodeXpuGroupRef.Name == groupID {
			for _, subject := range binding.Spec.Subjects {
				effectedNS[subject.Namespace] = make([]string, 0)
			}
		}
	}
	// find namespace using nxg
	for _, binding := range bindings {
		for _, subject := range binding.Spec.Subjects {
			if _, ok := effectedNS[subject.Namespace]; ok {
				effectedNS[subject.Namespace] = append(effectedNS[subject.Namespace], binding.Spec.NodeXpuGroupRef.Name)
			}
		}
	}
	nsQuotaSpec := make(map[string]models.ResourceQuotaSpec, 0)
	for k, v := range effectedNS {
		groupIDs := util.RemoveDuplicatesString(v)
		spec, err := c.NXPUGroupToResourceQuotaSpec(groupIDs)
		if err != nil {
			stdlog.Errorf("Failed to convert nxg to resource quota: %v", err)
			return nil, err
		}
		nsQuotaSpec[k] = *spec
	}

	return nsQuotaSpec, err
}

func (c *NXPUClient) GetAvaliableXPUsWithZeroQuota() map[string][]*models.ResourceQuotaItem {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.avaliableXPUs
}
