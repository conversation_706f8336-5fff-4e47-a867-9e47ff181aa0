package resourcequota

import (
	"fmt"
	"strconv"
	"testing"
	"time"

	"k8s.io/apimachinery/pkg/api/resource"
	"transwarp.io/aip/llmops-common/pkg/crd"
	"transwarp.io/applied-ai/central-auth-service/service/tenant/util"
)

func TestResourceQuantity(t *testing.T) {
	unit := resource.MustParse("16Mi")
	totalMem := resource.MustParse("256")
	unit.Mul(totalMem.Value())
	fmt.Printf("  unit.String(): %s\n", unit.String())
	fmt.Printf("  totalMem.String(): %s\n", totalMem.String())
	fmt.Printf("  totalMem.Value():  %d\n", totalMem.Value())

	finalValue := totalMem.Value()
	formattedResult := resource.NewQuantity(finalValue, resource.BinarySI)

	fmt.Printf("  Formatted result (NewQuantity): %s\n", formattedResult.String())
	fmt.Printf("  totalMem.Value():  %d\n", totalMem.Value())

	originalVal := formattedResult.Value() / unit.Value()
	fmt.Printf("  originVal: %s\n", strconv.FormatInt(originalVal, 10))
}

func TestCalculateByWeight(t *testing.T) {
	quantityStr := "100Gi"
	weight := 0.5
	result, err := util.CalculateByWeight(quantityStr, weight)
	if err != nil {
		t.Fatalf("Failed to calculate by weight: %v", err)
	}
	fmt.Printf("  result: %s\n", result)

	quantityStr = "8Gi"
	weight = 0.15
	result, err = util.CalculateByWeight(quantityStr, weight)
	if err != nil {
		t.Fatalf("Failed to calculate by weight: %v", err)
	}
	fmt.Printf("  result: %s\n", result)

	quantityStr = "8"
	weight = 0.3
	result, err = util.CalculateByWeight(quantityStr, weight)
	if err != nil {
		t.Fatalf("Failed to calculate by weight: %v", err)
	}
	fmt.Printf("  result: %s\n", result)
}

func TestDefaultQuota(t *testing.T) {
	crd.Init()

	manager := MustGetManager()
	if manager == nil {
		t.Fatal("Failed to get default resource quota")
	}

	quota := manager.GetDefaultQuota()
	fmt.Printf("  quota: %+v\n", quota)
}

func TestGetQuota(t *testing.T) {
	crd.Init()

	manager := MustGetManager()
	if manager == nil {
		t.Fatal("Failed to get default resource quota")
	}

	quota, err := manager.GetQuota("tenantzprtsjgblid")
	if err != nil {
		t.Fatalf("Failed to get quota: %v", err)
	}
	fmt.Printf("  quota: %+v\n", quota)
}

func TestUpdateConfigMap(t *testing.T) {
	crd.Init()

	manager := MustGetManager()
	if manager == nil {
		t.Fatal("Failed to get default resource quota")
	}

	time.Sleep(10 * time.Minute)

	quota, err := manager.GetQuota("tenantzprtsjgblid")
	if err != nil {
		t.Fatalf("Failed to get quota: %v", err)
	}
	fmt.Printf("  quota: %+v\n", quota)
}

func TestNXGoupToQuotaSpec(t *testing.T) {
	crd.Init()

	manager := MustGetManager()
	if manager == nil {
		t.Fatal("Failed to get default resource quota")
	}

	// cwz-test-1
	quota, err := manager.NxgToResourceQuotaSpec([]string{"541ba55a-38ee-4019-b7c4-6682a89134dd"})
	if err != nil {
		t.Fatalf("Failed to get quota: %v", err)
	}
	fmt.Printf("  quota: %+v\n", quota)
}
