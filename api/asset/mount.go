package asset

import (
	"net/http"

	restfulspec "github.com/emicklei/go-restful-openapi"
	"github.com/emicklei/go-restful/v3"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/central-auth-service/service"
	"transwarp.io/applied-ai/central-auth-service/service/project"
	"transwarp.io/applied-ai/central-auth-service/service/search"
)

func NewAssetAPI(prefix string, assetService *service.AssetService, ss *search.SearchSerivce, ps *project.ProjectService) *restful.WebService {
	return (&Resource{
		as: assetService,
		ss: ss,
		ps: ps,
	}).WebService(prefix)
}

type Resource struct {
	as *service.AssetService
	ss *search.SearchSerivce
	ps *project.ProjectService
}

type (
	Param     = string
	ParamDesc = string
	DataType  = string
)

const (
	ProjectIdParam     Param     = "project_id"
	ProjectIdParamDesc ParamDesc = "空间 ID"

	KeyParam     Param     = "key"
	KeyParamDesc ParamDesc = "关键字"

	RscTypesParam     Param     = "rsc_types"
	RscTypesParamDesc ParamDesc = "搜索类型(MWH:模型仓库，MLOPS:应用仓库, SAMPLE:样本仓库)"

	DataTypeString DataType = "string"
	DataTypeBool   DataType = "boolean"
	DataTypeInt    DataType = "integer"
	DataTypeFloat  DataType = "float"
	DataTypeFile   DataType = "file"
)

func (r *Resource) WebService(prefix string) *restful.WebService {
	tags := []string{"资产管理"}
	metaK, metaV := restfulspec.KeyOpenAPITags, tags

	ws := new(restful.WebService)
	ws.Path(prefix)
	ws.
		Consumes(restful.MIME_JSON, restful.MIME_OCTET).
		Produces(restful.MIME_JSON, restful.MIME_OCTET)

	ws.Route(ws.GET("/old-search").
		Doc("获取个子模块 统计的信息").
		Metadata(metaK, metaV).
		Param(ws.QueryParameter(ProjectIdParam, ProjectIdParamDesc).DataType(DataTypeString).Required(false)).
		Param(ws.QueryParameter(KeyParam, KeyParamDesc).DataType(DataTypeString).Required(true)).
		Param(ws.QueryParameter(RscTypesParam, RscTypesParamDesc).DataType(DataTypeString).Required(false)).
		To(r.GetModuleData).
		Writes(pb.AssetResp{}).
		Returns(http.StatusOK, "OK", []pb.AssetResp{}))

	ws.Route(ws.GET("/search").
		Doc("search model, applet, knowledgebase etc.").
		Metadata(metaK, metaV).
		Param(ws.QueryParameter("keyword", "search keyword").DataType(DataTypeString).Required(true)).
		Param(ws.QueryParameter("type", "assets type").DataType(DataTypeString).Required(false)).
		Param(ws.QueryParameter("project_id", "project id").DataType(DataTypeString).Required(false)).
		Param(ws.QueryParameter("creator", "creator").DataType(DataTypeString).Required(false)).
		Param(ws.QueryParameter("create_at_start", "create at start time").DataType(DataTypeString).Required(false)).
		Param(ws.QueryParameter("create_at_end", "create at end time").DataType(DataTypeString).Required(false)).
		Param(ws.QueryParameter("page", "page").DataType(DataTypeInt).Required(false)).
		Param(ws.QueryParameter("page_size", "page size").DataType(DataTypeInt).Required(false)).
		To(r.Search).
		Writes(SearchResp{}).
		Returns(http.StatusOK, "OK", SearchResp{}))

	return ws
}
