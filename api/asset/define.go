package asset

import (
	"strconv"

	"github.com/emicklei/go-restful/v3"
	"transwarp.io/aip/llmops-common/pb"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/central-auth-service/helper"
)

type SearchResultItem struct {
	ID   string `json:"id"`
	Name string `json:"name"`
	Type string `json:"type"`

	ProjectID   string `json:"project_id"`
	ProjectName string `json:"project_name"`
	TenantID    string `json:"tenant_id"`
	TenantName  string `json:"tenant_name"`
}

type SearchResp struct {
	Total    int                `json:"total"`
	Page     int                `json:"page"`
	PageSize int                `json:"page_size"`
	Items    []SearchResultItem `json:"items"`
}

func (r *Resource) GetModuleData(request *restful.Request, response *restful.Response) {
	projectId := request.QueryParameter(ProjectIdParam)

	key := request.QueryParameter(KeyParam)
	if len(key) == 0 {
		helper.ErrorResponse(response, stderr.BadRequest.Error("缺少请求参数 key"))
		return
	}

	rscTypes := request.QueryParameter(RscTypesParam)

	assetReq := &pb.AssetReq{
		ProjectId: projectId,
		Key:       key,
	}

	assetResp, err := r.as.GetModuleData(assetReq, rscTypes)

	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "获取所有数据异常"))
		return
	}
	helper.SuccessStringResponse(response, assetResp)
}

func (r *Resource) Search(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)

	searchReq := &pb.SearchAssetReq{}
	keyword := request.QueryParameter("keyword")
	if len(keyword) == 0 {
		helper.ErrorResponse(response, stderr.BadRequest.Errorf("no search keyword"))
		return
	}
	searchReq.Keyword = keyword
	typeStrs := request.QueryParameters("type")
	if len(typeStrs) > 0 {
		for _, typeStr := range typeStrs {
			if val, ok := stringToSearchAssetType(typeStr); ok {
				searchReq.Type = append(searchReq.Type, val)
			} else {
				helper.ErrorResponse(response, stderr.BadRequest.Errorf("invalid search type %s", typeStr))
				return
			}
		}
	}
	projectIDs := request.QueryParameters("project_id")
	if len(projectIDs) != 0 {
		searchReq.ProjectId = projectIDs
	}
	creator := request.QueryParameter("creator")
	if creator != "" {
		searchReq.Creator = creator
	}
	createAtStartStr := request.QueryParameter("create_at_start")
	if createAtStartStr != "" {
		createAtStart, err := strconv.ParseInt(createAtStartStr, 10, 64)
		if err != nil {
			helper.ErrorResponse(response, stderr.BadRequest.Errorf("invalid create at start %s", createAtStartStr))
			return
		}
		searchReq.CreatedAtStart = int64(createAtStart)
	}
	createAtEndStr := request.QueryParameter("create_at_end")
	if createAtEndStr != "" {
		createAtEnd, err := strconv.ParseInt(createAtEndStr, 10, 64)
		if err != nil {
			helper.ErrorResponse(response, stderr.BadRequest.Errorf("invalid create at end %s", createAtEndStr))
			return
		}
		searchReq.CreatedAtEnd = createAtEnd
	}
	pageStr := request.QueryParameter("page")
	if pageStr != "" {
		helper.ErrorResponse(response, stderr.BadRequest.Errorf("invalid page %s", pageStr))
		return
	}
	page, err := strconv.Atoi(pageStr)
	if err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Errorf("invalid page %s", pageStr))
		return
	}
	searchReq.Page = int32(page)
	pageSizeStr := request.QueryParameter("page_size")
	if pageSizeStr != "" {
		helper.ErrorResponse(response, stderr.BadRequest.Errorf("invalid page size %s", pageSizeStr))
		return
	}
	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil {
		helper.ErrorResponse(response, stderr.BadRequest.Errorf("invalid page size %s", pageSizeStr))
		return
	}
	searchReq.PageSize = int32(pageSize)

	username := helper.ParseUsername(request)
	projs, err := r.ps.ListProjects(username, "")
	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "list porjects failed"))
		return
	}
	projIDs := make([]string, 0)
	for _, p := range projs {
		projIDs = append(projIDs, p.ProjectId)
	}

	searchResp, err := r.ss.AggregateSearch(ctx, searchReq)

	if err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "search error"))
		return
	}
	helper.SuccessStringResponse(response, searchResp)
}

func stringToSearchAssetType(s string) (pb.SearchAssetType, bool) {
	if val, ok := pb.SearchAssetType_value[s]; ok {
		return pb.SearchAssetType(val), true
	}

	return pb.SearchAssetType(0), false
}
