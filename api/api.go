package api

import (
	"strings"

	"transwarp.io/applied-ai/central-auth-service/api/metrics"
	"transwarp.io/applied-ai/central-auth-service/api/notice"
	metricssvc "transwarp.io/applied-ai/central-auth-service/service/metrics"

	"transwarp.io/applied-ai/central-auth-service/api/alert_history"
	"transwarp.io/applied-ai/central-auth-service/api/alerting"
	"transwarp.io/applied-ai/central-auth-service/api/custom"
	"transwarp.io/applied-ai/central-auth-service/api/monitor"
	"transwarp.io/applied-ai/central-auth-service/api/oauth2"
	"transwarp.io/applied-ai/central-auth-service/api/portal"
	rbacapi "transwarp.io/applied-ai/central-auth-service/api/rbac"

	stdexamine "transwarp.io/applied-ai/aiot/vision-std/examine"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/aiot/vision-std/toolkit"
	"transwarp.io/applied-ai/central-auth-service/api/examine"
	"transwarp.io/applied-ai/central-auth-service/api/gpu_spec"

	"github.com/emicklei/go-restful/v3"

	utils "transwarp.io/applied-ai/aiot/vision-std/auth"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdsrv"
	"transwarp.io/applied-ai/central-auth-service/api/article"
	"transwarp.io/applied-ai/central-auth-service/api/asset"
	"transwarp.io/applied-ai/central-auth-service/api/audit"
	"transwarp.io/applied-ai/central-auth-service/api/auth"
	operator "transwarp.io/applied-ai/central-auth-service/api/guardian"
	"transwarp.io/applied-ai/central-auth-service/api/statistics"
	"transwarp.io/applied-ai/central-auth-service/api/swagger"
	tapi "transwarp.io/applied-ai/central-auth-service/api/tenant"
	"transwarp.io/applied-ai/central-auth-service/api/ugly"
	"transwarp.io/applied-ai/central-auth-service/api/user"
	"transwarp.io/applied-ai/central-auth-service/api/usermgr"
	"transwarp.io/applied-ai/central-auth-service/conf"
	"transwarp.io/applied-ai/central-auth-service/dao"
	"transwarp.io/applied-ai/central-auth-service/dao/cache"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/service"
	alsvc "transwarp.io/applied-ai/central-auth-service/service/alerting"
	mosvc "transwarp.io/applied-ai/central-auth-service/service/monitor"
	"transwarp.io/applied-ai/central-auth-service/service/project"
	"transwarp.io/applied-ai/central-auth-service/service/rbac"
	"transwarp.io/applied-ai/central-auth-service/service/search"
	stats "transwarp.io/applied-ai/central-auth-service/service/statistics"
	"transwarp.io/applied-ai/central-auth-service/service/tenant"
	util "transwarp.io/applied-ai/central-auth-service/utils"
	session "transwarp.io/applied-ai/central-auth-service/utils/auth"
	"transwarp.io/applied-ai/central-auth-service/utils/auth/cas/server"
)

const (
	Action        = "REF"
	ServiceType   = "TENANT"
	TYPE          = "COMPONENT"
	Principal     = "admin"
	PrincipalType = "USER"
	tdcsys        = "tdcsys"
)

var (
	exceptURIs = map[string]struct{}{
		"/api/login":  {}, // FIXME 为了适配 SOPHON PORTAL 的冗余代码
		"/api/logout": {}, // FIXME 为了适配 SOPHON PORTAL 的冗余代码
	}
	exceptModules = map[string]struct{}{
		helper.PathSwagger: {},
		helper.PathAuth:    {},
		"/api/users":       {}, // FIXME 为了适配 SOPHON PORTAL 的冗余代码
		"/api/v1/oauth2":   {},
		"/api/v1/rbac":     {},
	}
	rbacUs *rbac.UserService
)

func Mount() error {
	db, err := dao.ConnectDB(conf.C.DB)
	if err != nil {
		return err
	}
	dao.DB = db
	ss, err := session.NewSessionStore(conf.C.SessionStore)
	if err != nil {
		return err
	}
	cache := cache.GetDataCache()

	restful.Add(swagger.NewSwaggerAPI(helper.PathSwagger))
	as := service.NewAuthService(db, conf.C.Auth.AllowMultiClientLoginAtSameTime)
	us := service.NewUserService(db)
	uis := rbac.NewUserImportService(db)
	assetS := service.NewAssetService(db, cache)

	ps := project.NewProjectService(db)
	pms := project.NewProjectMemberService(db)
	pcs := project.NewProjectCategoryService(db)

	tenantService := tenant.NewTenantService(db)

	rbacUs = rbac.NewUserService(db)
	ugs := rbac.NewUserGroupService(db)
	rs := rbac.NewRoleService(db)
	rbacPs := rbac.NewPermissionService(db)
	ts := service.NewAccessTokenService(db, ss)
	sus := service.NewUserService(db)
	pkg, _ := operator.DiscoverTDCGuardianPackage("default")
	// guardianClient, _ := guardianV2.InitGuardianV2Client(pkg.GuardianServerAddress)
	guardianClient, _ := pkg.GetGuardianClient()
	apiClient := guardianClient.GetGuardianAPIClient()
	uas := apiClient.UsersApi
	gas := apiClient.GroupsApi
	gat := apiClient.AccessTokensApi
	// uas := new(guardianv2.UsersApiService)
	// gas := new(guardianv2.GroupsApiService)

	searchSerivce := search.NewSearchService()
	statsService := stats.NewStatisticsService(us, ps)

	restful.Add(ugly.NewUglyAPI(us, ts)) // FIXME 为了适配 SOPHON PORTAL 的冗余代码
	restful.Add(auth.NewAuthAPI(helper.PathAuth, as, us, ts))
	restful.Add(oauth2.NewOAuth2API(helper.PathV1+"/oauth2", rbacUs))
	restful.Add(asset.NewAssetAPI(helper.PathAsset, assetS, searchSerivce, ps))
	restful.Add(user.NewUserAPI(helper.PathUser, us, ts))
	restful.Add(usermgr.NewUserManagerAPI("/api/v1", rbacUs, uis, ugs, rs, rbacPs, uas, gas, gat, sus))
	restful.Add(usermgr.NewProjectManagerAPI("/api/v1", ps, pms, rs, pcs))
	restful.Add(tapi.NewTenantAPI(helper.PathTenant, tenantService, ps))
	restful.Add(examine.NewExamineAPI(helper.PathExamineFlow, db))
	restful.Add(custom.NewCustomAPI(helper.PathCustom, db))
	restful.Add(alert_history.NewAlertHistoryAPI(helper.PathAlertHistory))
	restful.Add(notice.NewNoticeAPI(helper.PathNotice, db))
	restful.Add(statistics.NewStatisticsAPI("/api/v1", statsService))
	restful.Add(rbacapi.NewRbacApi("/api/v1"))

	articleSvc := service.NewArticleService(db)
	restful.Add(article.NewArticleAPI("/api/v1", articleSvc))

	gpuSettingSvc := service.NewGpuSpecService(db)
	restful.Add(gpu_spec.NewGpuSpecAPI("/api/v1", gpuSettingSvc))

	if !conf.C.Server.LiteMode {
		restful.Add(audit.NewAuditAPI("/api/v1", service.NewAuditRecordService()))
	}

	restful.Add(alerting.NewAlertingAPI(alsvc.NewAlertService()))

	restful.Add(monitor.NewMonitorAPI("/api/v1", mosvc.NewMonitorService(), ps, tenantService))

	restful.Add(metrics.NewMetricsAPI("/api/v1", metricssvc.NewMetricsStatsService()))

	restful.Add(portal.NewPortalInfoAPI("/api/v1", service.NewPortalInfoService(db)))

	// fixme 接口参数返回值 都设置成返回 string
	restful.RegisterEntityAccessor(restful.MIME_JSON, stdsrv.DefaultProtoJsonAccessor())

	for _, ws := range restful.RegisteredWebServices() {
		// add filters for ws
		ws.Filter(AuthCheck)
		ws.Filter(stdexamine.ExamineCheck)
	}
	return nil
}

func AuthCheck(request *restful.Request, response *restful.Response, chain *restful.FilterChain) {
	if needsCheckURI(request.Request.URL.Path) {
		if !auth.GetAuthHandler().IsAuthenticated(response.ResponseWriter, request.Request) {
			helper.ErrorResponse(response, stderr.Unauthenticated.Error("fail to pass the authentication"))
			return
		}
		token, err := utils.ParseTokenFromRequest(request.Request)
		if err != nil {
			helper.ErrorResponse(response, stderr.Unauthenticated.Error(err.Error()))
			return
		}
		utils.SetAuthContext(request, &token)

		tname := token.GetUsername()
		tgc, err := request.Request.Cookie(server.TGTCookieName)
		if err == nil {
			// 用户有效期更新
			go func(name, tk string) {
				_ = rbacUs.UpdateUserStatusByExp(name, toolkit.MD5Bytes([]byte(token.InternalToken())), tk)
			}(tname, tgc.Value)
		}

		// 账号白名单校验
		ip := util.ClientIP(request)
		passed, wips, err := rbacUs.InWhiteIPs(request.Request.Context(), tname, ip)
		if err != nil {
			helper.ErrorResponse(response, stderr.Wrap(err, "IP白名单校验失败"))
			return
		}
		if !passed {
			wip := strings.Join(wips, ", ")
			stdlog.Warnf("account %s access from %s is not allowed: %s", tname, ip, wip)
			helper.ErrorResponse(response, stderr.Unauthorized_IPWhiteList.Errorf(wip))
			return
		}
	}
	chain.ProcessFilter(request, response)
}

func needsCheckURI(uri string) bool {
	for exceptURI := range exceptURIs {
		if uri == exceptURI {
			return false
		}
	}
	for exceptModule := range exceptModules {
		if strings.HasPrefix(uri, exceptModule) {
			return false
		}
	}
	return true
}
