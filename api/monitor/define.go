package monitor

import (
	"strconv"

	"github.com/emicklei/go-restful/v3"
	"transwarp.io/applied-ai/aiot/vision-std/stderr"
	"transwarp.io/applied-ai/aiot/vision-std/stdlog"
	"transwarp.io/applied-ai/central-auth-service/clients"
	"transwarp.io/applied-ai/central-auth-service/helper"
	"transwarp.io/applied-ai/central-auth-service/service/monitor"
	"transwarp.io/applied-ai/central-auth-service/service/project"
	"transwarp.io/applied-ai/central-auth-service/service/tenant"
	"transwarp.io/applied-ai/central-auth-service/service/tenant/util"
)

const (
	PathParamServiceId          = "sid"
	QueryParamProjectId         = "project_id"
	QueryParamUser              = "user_id"
	QueryParamServiceId         = "service_id"
	QueryParamServiceSourceType = "service_source_type"
	QueryParamFrom              = "from"
	QueryParamTo                = "to"
	QueryParamStep              = "step"
	QueryParamTab               = "tab"
	QueryParamType              = "type"
	QueryParamTopK              = "topk"
	PathParamCardDeviceUUID     = "device_uuid"
	QueryParamName              = "name"
	QueryParamPage              = "page"
	QueryParamPageSize          = "page_size"
	QueryParamNode              = "node"
)

var (
	defaultType = "service"
)

func NewMonitorAPI(root string, ms *monitor.MonitorService, ps *project.ProjectService, ts tenant.TenantService) *restful.WebService {
	return (&Resource{ms: ms, ps: ps, ts: ts}).WebService(root)
}

type Resource struct {
	ms *monitor.MonitorService
	ps *project.ProjectService
	ts tenant.TenantService
}

func (r *Resource) ListSvcMetrics(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	sid := request.PathParameter(PathParamServiceId)

	st, ok := helper.ParseSvcSourceTypeFromRequest(request)
	if !ok {
		helper.ErrorResponse(response, stderr.InvalidParam.Errorf("service source type is invalid"))
		return
	}

	req := &monitor.ListSvcMetricsReq{
		ServiceId:         sid,
		ServiceSourceType: st,
	}
	rsp, err := r.ms.ListSvcMetrics(ctx, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) QuerySvcDashboardData(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)

	st, ok := helper.ParseSvcSourceTypeFromRequest(request)
	if !ok {
		helper.ErrorResponse(response, stderr.InvalidParam.Errorf("service source type is invalid"))
		return
	}

	tab, ok := helper.ParseDashboardTabTypeFromRequest(request)
	if !ok {
		helper.ErrorResponse(response, stderr.InvalidParam.Errorf("dashboard tab is invalid"))
		return
	}

	queryReq, err := parseQueryRequest(request)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	svcQueryReq := &monitor.SvcQueryRequest{
		QueryRequest:      *queryReq,
		ServiceId:         request.PathParameter(PathParamServiceId),
		ServiceSourceType: st,
		DashboardTab:      tab,
	}
	rsp, err := r.ms.QuerySvcDashboardData(ctx, svcQueryReq)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) CreateSvcGrafanaDashboard(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)

	svcId := request.PathParameter(PathParamServiceId)
	if svcId == "" {
		helper.ErrorResponse(response, stderr.InvalidParam.Errorf("service ID cannot be empty"))
		return
	}

	svcName := request.QueryParameter("name")

	uri, err := r.ms.GetOrPublishSvcDashboard(ctx, svcId, svcName)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	rsp := &monitor.GrafanaPublicDashboardRsp{
		URI: uri,
	}
	helper.SuccessResponse(response, rsp)
}

func (r *Resource) ListGlobalGPUDashboardOptions(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)

	options, err := r.buildGPUOptions(ctx)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	helper.SuccessResponse(response, options)
}
func (r *Resource) QueryGlobalGPUOverview(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)

	filter := new(monitor.GlobalGPUFilter)
	if err := request.ReadEntity(filter); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "parse req  err"))
		return
	}
	realFilter := r.convertFilter(ctx, filter)

	var overview = r.BuildGPUOverview(ctx, realFilter)
	helper.SuccessResponse(response, overview)

}

func (r *Resource) QueryGlobalGPUCardBase(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	tenantKey := "tenantId"
	tenantId := request.QueryParameter(tenantKey)
	if len(tenantId) == 0 {
		tenantKey = "tenant_id"
		tenantId = request.QueryParameter(tenantKey)
	}

	filter := new(monitor.GlobalGPUFilter)
	if err := request.ReadEntity(filter); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "parse req  err"))
		return
	}

	realFilter := r.convertFilter(ctx, filter)

	if len(tenantId) != 0 {
		realFilter.ProjectIds = append(realFilter.ProjectIds, tenantId)
	}

	base := r.BuildGlobalGPUCardBase(ctx, realFilter)

	helper.SuccessResponse(response, base)
}

func (r *Resource) QueryGlobalGPUInfo(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	uuid := request.PathParameter(PathParamCardDeviceUUID)
	node := request.QueryParameter(QueryParamNode)
	//兼容 ascend id 为 hamiId
	info, err := clients.ExpenseCli.GetNpuInfos(ctx)
	hamiId := uuid
	if err == nil {
		for _, npuInfo := range info.Infos {
			if npuInfo.HamiID == uuid && npuInfo.Node == node {
				hamiId = npuInfo.VDIEID
			}
			if npuInfo.VDIEID == uuid {
				uuid = npuInfo.HamiID
			}
		}
	}

	realFilter := r.newFilter(0, hamiId)
	req := r.convert2Filter(ctx, realFilter, monitor.GlobalPanelTypeResourceUsage)

	rsp, err := r.ms.QueryGlobalData(ctx, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	usage := r.BuildGPUsage(rsp)
	detail := r.BuildGPUInfo(ctx, uuid, usage)

	helper.SuccessResponse(response, &monitor.GPUInfo{
		Detail: detail,
		Usage:  *usage,
	})
}
func (r *Resource) QueryGlobalBaseOverview(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	filter := new(monitor.GlobalGPUFilter)
	if err := request.ReadEntity(filter); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "parse req  err"))
		return
	}
	realFilter := r.convertFilter(ctx, filter)
	req := r.convert2Filter(ctx, realFilter, monitor.GlobalPanelTypeBaseOverview)

	rsp, err := r.ms.QueryGlobalData(ctx, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	base := r.BuildBaseOverview(rsp)

	helper.SuccessResponse(response, base)
}

func (r *Resource) QueryGlobalGPUUsageTrends(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	filter := new(monitor.GlobalGPUFilter)
	if err := request.ReadEntity(filter); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "parse req  err"))
		return
	}
	realFilter := r.convertFilter(ctx, filter)
	req := r.convert2Filter(ctx, realFilter, monitor.GlobalPanelTypeGpuTRENDS)

	rsp, err := r.ms.QueryGlobalData(ctx, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	r.EnrichSortedLegends(ctx, realFilter, rsp)

	helper.SuccessResponse(response, rsp)
}

func (r *Resource) QueryGlobalBaseUsageTrends(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	filter := new(monitor.GlobalGPUFilter)
	if err := request.ReadEntity(filter); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "parse req err"))
		return
	}
	originFilter := &monitor.GlobalGPUFilter{
		ProjectIds: filter.ProjectIds,
	}
	realFilter := r.convertFilter(ctx, originFilter)
	req := r.convert2Filter(ctx, realFilter, monitor.GlobalPanelTypeBaseTrend)

	rsp, err := r.ms.QueryGlobalData(ctx, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	r.ConvertBaseUsageName(ctx, rsp)

	helper.SuccessResponse(response, rsp)
}

func (r *Resource) QueryGlobalUsageRankings(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	filter := new(monitor.GlobalGPUFilter)
	if err := request.ReadEntity(filter); err != nil {
		helper.ErrorResponse(response, stderr.Internal.Cause(err, "parse req err"))
		return
	}
	realFilter := r.convertFilter(ctx, filter)

	queryType := request.QueryParameter(QueryParamType)
	globalType := monitor.GlobalPanelTypeBaseRanking

	topK := request.QueryParameter(QueryParamTopK)
	if topK == "" {
		topK = "5"
	}
	topKInt, err := strconv.Atoi(topK)
	if err != nil || topKInt <= 0 {
		stdlog.Warnf("cannot convert topK to int, will set default number : 5")
		topKInt = 5
	}

	if realFilter.Topk == 0 {
		realFilter.Topk = topKInt
	}

	req := r.convert2Filter(ctx, realFilter, globalType)
	if queryType == defaultType || filter.FilterType == defaultType {
		req.GlobalPanelType = monitor.GlobalPanelTypeServiceRanking
		req.Service = defaultType
		namespace, err := util.GetCurrentNamespace()
		if err != nil {
			helper.ErrorResponse(response, err)
			return
		}
		req.Namespace = namespace
	}

	rsp, err := r.ms.QueryGlobalData(ctx, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}

	ret := r.BuildRankings(ctx, rsp)

	helper.SuccessResponse(response, ret)
}

func (r *Resource) QueryGlobalSvcOverview(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)

	realFilter := r.newFilter(0, "")
	req := r.convert2Filter(ctx, realFilter, monitor.GlobalPanelTypeSvcOverview)
	namespace, err := util.GetCurrentNamespace()
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req.NamespaceName = namespace

	rsp, err := r.ms.QueryGlobalData(ctx, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	base := r.BuildSvcOverview(ctx, rsp)

	helper.SuccessResponse(response, base)
}

func (r *Resource) QueryGlobalSvcList(request *restful.Request, response *restful.Response) {
	ctx := helper.GenNewCtx(request)
	uuid := request.PathParameter(PathParamCardDeviceUUID)
	node := request.QueryParameter(QueryParamNode)
	//兼容 ascend id 为 hamiId
	info, err := clients.ExpenseCli.GetNpuInfos(ctx)
	if err == nil {
		for _, npuInfo := range info.Infos {
			if npuInfo.HamiID == uuid && npuInfo.Node == node {
				uuid = npuInfo.VDIEID
				break
			}
		}
	}
	name := request.QueryParameter("name")

	realFilter := r.newFilter(0, uuid)
	realFilter.RefName = name
	req := r.convert2Filter(ctx, realFilter, monitor.GlobalPanelTypeSvcList)

	rsp, err := r.ms.QueryGlobalData(ctx, req)
	if err != nil {
		helper.ErrorResponse(response, err)
		return
	}
	req.GlobalPanelType = monitor.GlobalPanelTypeSvcDetail
	list := r.BuildSvcList(ctx, rsp, req)
	helper.SuccessResponse(response, list)

}

func parseQueryRequest(request *restful.Request) (*monitor.QueryRequest, error) {
	from := request.QueryParameter(QueryParamFrom)
	to := request.QueryParameter(QueryParamTo)
	step := request.QueryParameter(QueryParamStep)

	if from == "" || to == "" || step == "" {
		return nil, stderr.InvalidParam.Errorf("missing required query parameters")
	}

	stepInt, err := strconv.Atoi(step)
	if err != nil {
		return nil, stderr.InvalidParam.Errorf("invalid step parameter")
	}

	return &monitor.QueryRequest{
		From: from,
		To:   to,
		Step: stepInt,
	}, nil
}
